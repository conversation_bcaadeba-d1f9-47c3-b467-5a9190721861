#ifndef __PROCESS_INFER_HPP__
#define __PROCESS_INFER_HPP__

#include <opencv2/opencv.hpp>
#include "prep.hpp"

namespace prep {

struct Out {
    Out() = default;
    explicit Out(std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>>&& ret): val(ret) {}
    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> val;
};

struct ImageIn {
    ImageIn(const cv::Mat &image, const std::string &name):
        image(image), image_path(name) {}

    cv::Mat image;
    const std::string image_path;
};

class Slice {
public:
    Slice(float left_alpha, float left_beta,
          float right_alpha, float right_beta,
          int img_width, int img_height,
          int left_min, int left_max,
          int middle_min, int middle_max,
          int right_min, int right_max,
          int max_batch_size,
          int mem_align,
          int max_block_size,
          int max_queue_imgs,
          const std::string& save_img):
        __max_batch_size(max_batch_size),
        __max_queue_imgs(max_queue_imgs),
        __micro_sec_laytency(500),
        _success(true),
        __prep_infer(std::make_shared<PrepImpl>(left_alpha, left_beta,
                                                right_alpha, right_beta,
                                                img_width, img_height,
                                                left_min, left_max,
                                                middle_min, middle_max,
                                                right_min, right_max,
                                                max_batch_size, mem_align, 
                                                max_block_size, save_img)) {

            cudaStreamCreate(&__stream);
        }

    inline bool success() {
        return _success;
    }

    inline int get_batch_size() {
        return __max_batch_size;
    }

    //void single_img_slice();
    bool multi_thread_start();
    //void multi_thread_infer(std::queue<std::pair<bool, std::string>> &files);
    //void single_worker(std::queue<std::pair<bool, std::string>>& files);
    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> multi_thread_infer(std::queue<std::pair<bool, std::string>> &files);
    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> single_worker(std::tuple<bool, cv::Mat, std::string>& data);

private:
    std::string __get_image_name(const std::string &path);
    std::shared_ptr<PrepImpl> __prep_infer;

private:

    int __max_queue_imgs;
    int __micro_sec_laytency;

    int __max_batch_size;
    std::vector<cp::ImageIn> __images;

    cudaStream_t __stream;
    cp::Instance<cp::CpmImage, slice::Image, PrepImpl, Out> _cpm_infer;

    bool _success;
};

} // namespace prep

#endif
