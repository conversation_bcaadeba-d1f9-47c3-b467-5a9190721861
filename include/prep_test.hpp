#ifndef __PRVTEST_HPP__
#define __PRVTEST_HPP__

#include "preprocess_infer.hpp"

#include <random>
#include <chrono>

class SliceTest {
public:
    SliceTest(const std::string &image_path,
              const std::string &save_path,
              float left_alpha, float left_beta,
              float right_alpha, float right_beta,
              int img_width, int img_height,
              int left_min, int left_max,
              int middle_min, int middle_max,
              int right_min, int right_max,
              int mem_align,
              int max_block_size,
              int max_batch_size,
              int max_queue_imgs):
        _image_path(image_path), _save_path(save_path),
        __infer(new prep::Slice(left_alpha, left_beta,
                                right_alpha, right_beta,
                                img_width, img_height, 
                                left_min, left_max, 
                                middle_min, middle_max, 
                                right_min, right_max,
                                max_batch_size, mem_align, max_block_size, 
                                max_queue_imgs, save_path)) {}

    ~SliceTest() = default;

    /* multi-thread infer, read image in lib */
    //int thread_img_path();
    int single_img_slice();

private:
    void __get_files(const std::string &path, bool left);

private:
    std::unique_ptr<prep::Slice> __infer;
    std::queue<std::string> __l_files;
    std::queue<std::string> __r_files;

    const std::string _image_path;
    const std::string _save_path;
};

#endif
