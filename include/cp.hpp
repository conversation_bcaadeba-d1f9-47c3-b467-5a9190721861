#ifndef __CP_HPP__
#define __CP_HPP__

// Comsumer Producer Model

#ifdef linux
#include <pthread.h>
#endif

#include <algorithm>
#include <condition_variable>
#include <future>
#include <memory>
#include <queue>
#include <thread>
#include <chrono>
#include <vector>
#include <utility>
#include <opencv2/opencv.hpp>
#include <cuda_runtime.h>

namespace cp {

struct ImageIn {
    ImageIn(const cv::Mat &image, const std::string &name):
        image(image), image_path(name) {}

    cv::Mat image;
    const std::string image_path;
};

struct CpmImage {
    CpmImage(const cv::Mat &in_img, 
             bool left, const std::string image_path) : 
        img(in_img), left(left), image_path(image_path) {}

    std::string image_path;
    const cv::Mat img;
    bool left;
};

template <typename Input, typename ForwardIn, typename Model, typename Output>
class Instance {
protected:

    struct Item {
        Item() = default;
        Item(Input &in): input(in) {}
        Item(Input &&in): input(in) {}

        Input input;
        std::shared_ptr<std::promise<Output>> pro;
    };

    std::queue<Item> input_queue_;
    std::shared_ptr<std::thread> worker_;

    std::mutex queue_lock_;
    std::condition_variable cond_;

    bool run_ = false;
    int micro_sec_laytency = 500;
    int max_items_processed_ = 0;
    int images_in_queue_ = 0;
    bool _visualize_affine=false;

    int max_queue_imgs;
    cudaStream_t stream_;

public:
    Instance() {}
    virtual ~Instance() { 
        stop(); }

    void stop() {
        run_ = false;
        cond_.notify_one();
        //{
        //    std::unique_lock<std::mutex> l(queue_lock_);
        //    while (!input_queue_.empty()) {
        //      auto &item = input_queue_.front();
        //      input_queue_.pop();
        //    }
        //};

        if (worker_) {
            worker_->join();
            worker_.reset();
        }
    }

    virtual bool put_items_and_wait(std::vector<std::shared_future<Output>>& output,
                                    std::queue<std::pair<bool, std::string>>& files) {
        {
            std::unique_lock<std::mutex> l(queue_lock_);
            cond_.wait(l, [&]() { return !run_ || input_queue_.size() < max_queue_imgs;});

            if (!run_) return false;

            int diff = max_queue_imgs - input_queue_.size();
            for (int i = 0; i < diff && !files.empty(); ++i) {

                //auto path = files.front();
                auto element = files.front();
                bool left = element.first;
                auto path = element.second;
                //cv::Mat mat = cv::imread(path);
                // read gray img
                cv::Mat mat = cv::imread(path, cv::IMREAD_GRAYSCALE);

                // avoid emtpy image
                if(mat.empty()) {
                    mat.data = nullptr;
                    mat.rows = 0;
                    mat.cols= 0;
                }

                files.pop();

                Item item(Input(mat, left, path));
                item.pro.reset(new std::promise<Output>());
                output.emplace_back(item.pro->get_future());
                input_queue_.push(item);

                images_in_queue_ = input_queue_.size();
            }
        } // lock

        cond_.notify_one();
        /* 
            NOTE: latency here 
            this will not affect efficiency 
            just make sure worker thread can definitly get the lock
        */
        std::this_thread::sleep_for(std::chrono::microseconds(this->micro_sec_laytency));
        return true;
    }

    virtual std::vector<std::shared_future<Output>> commits(std::queue<std::pair<bool, std::string>> &files) {
        std::vector<std::shared_future<Output>> output;

        while(!files.empty()) {
            put_items_and_wait(output, files);
        }

        return std::move(output);  
    }

    template <typename LoadMethod>
    bool start(const LoadMethod &loadmethod,  // LoadMethod: YoloInfer
               cudaStream_t &stream,
               int micro_sec_laytency=500,
               int max_items_processed = 1, 
               int max_queue_imgs = 160) {
        stop();

        this->stream_ = stream;
        this->max_items_processed_ = max_items_processed;
        this->max_queue_imgs = max_queue_imgs;
        this->micro_sec_laytency = micro_sec_laytency;

        std::promise<bool> status;
        worker_ = std::make_shared<std::thread>(&Instance::worker<LoadMethod>, 
                                                this,
                                                std::ref(loadmethod), 
                                                std::ref(status));

        return status.get_future().get();
    }

private:
    template <typename LoadMethod>
    void worker(const LoadMethod &loadmethod, std::promise<bool> &status) {
        std::shared_ptr<Model> model = loadmethod;
        if (model == nullptr) {
            status.set_value(false);
            return;
        }

        run_ = true;
        status.set_value(true);

        std::vector<Item> fetch_items;
        int img_in_queue = 0;
        while (get_items_and_wait(fetch_items, img_in_queue)) {

            if(fetch_items.size()==0) break;

            // max_items_processed_ => max_batch_size
            int blocks = int((img_in_queue + max_items_processed_ -1)/max_items_processed_);

            int left_imgs = img_in_queue;
            int img_in_block = 0;

            auto item_itr = fetch_items.begin();
            std::vector<ForwardIn> inputs;

            for(int i = 0; i<blocks; ++i) {

                if(left_imgs == 0) break;

                img_in_block = max_items_processed_;
                if(left_imgs < max_items_processed_) {
                    img_in_block = left_imgs;
                } else {
                    left_imgs = img_in_queue - img_in_block;
                }

                img_in_queue -= img_in_block;

                inputs.resize(img_in_block);
                std::transform(item_itr,
                               item_itr+img_in_block, 
                               inputs.begin(),
                               [](Item &item) { 
                                   return ForwardIn(item.input.img.data, 
                                                    item.input.img.cols,
                                                    item.input.img.rows,
                                                    item.input.left,
                                                    item.input.image_path);
                               });

                auto start = std::chrono::high_resolution_clock::now();
                auto ret = model->forwards(inputs, stream_);
                auto end = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
                float t = static_cast<float>(duration.count());
                printf("forwards: %f ms per img\n", t/inputs.size()/1000);

                int cnt = 0;
                for (int i = 0; i < img_in_block; ++i) {
                    if(inputs[i].left) {
                        (*(item_itr+i)).pro->set_value(Output({ret[cnt], ret[cnt+1]}));
                        cnt += 2;
                    } else {
                        (*(item_itr+i)).pro->set_value(Output({ret[cnt]}));
                        cnt += 1;
                    }
                }

                item_itr += img_in_block;
                inputs.clear();
            }

            fetch_items.clear();
        }

        model.reset();
        run_ = false;
    }

    virtual bool get_items_and_wait(std::vector<Item> &fetch_items, 
                                    int &img_in_queue) {
        {
            std::unique_lock<std::mutex> l(queue_lock_);
            cond_.wait(l, [&]() { return !run_ || !input_queue_.empty(); });

            //if (!run_) return false;
            fetch_items.clear();
            img_in_queue = images_in_queue_;
            for (int i = 0; i < images_in_queue_ && !input_queue_.empty(); ++i) {
                fetch_items.emplace_back(std::move(input_queue_.front()));
                input_queue_.pop();
            }
        }

        cond_.notify_one();
        return true;
    }

    virtual std::string get_image_name(const std::string &path) {
        // '/' for linux ; '\\' for windows
        size_t position = path.find_last_of("/\\");

        // get the last split string
        return path.substr(position+1, path.length()-position-1);
    }

};

} // namespace cp

#endif  // __CPM_HPP__
