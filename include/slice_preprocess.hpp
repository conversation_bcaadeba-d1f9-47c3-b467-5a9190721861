#ifndef __S_PRE_HPP
#define __S_PRE_HPP

#include <opencv2/opencv.hpp>
#include "slice_memory.hpp"

namespace slice {

struct Image {
    std::string image_name;
    size_t width = 0, height = 0;
    bool left;
    const void *bgrptr = nullptr;

    Image() = default;
    Image(const void *bgrptr, 
          int width, int height, bool left,
          const std::string image_name) : 
        bgrptr(bgrptr), width(width), height(height), left(left),
        image_name(image_name) {}
};

class Preprocess {
public:
    Preprocess() = default;
    Preprocess(float left_alpha, float left_beta,
               float right_alpha, float right_beta,
               int img_width, int img_height,
               int left_min, int left_max,
               int middle_min, int middle_max,
               int right_min, int right_max,
               size_t mem_align, int max_block_size,
               bool visualize=false): 
               _left_alpha(left_alpha), _left_beta(left_beta),
               _right_alpha(right_alpha), _right_beta(right_beta),
               _img_width(img_width), _img_height(img_height),
               _left_min(left_min), _left_max(left_max),
               _middle_min(middle_min), _middle_max(middle_max),
               _right_min(right_min), _right_max(right_max),
               _visualize_affine(visualize),
               __max_block_size(max_block_size), __mem_align(mem_align) {}

    ~Preprocess() {}

    void preprocess(const Image &image,
                    std::shared_ptr<Memory<uint8_t>> preprocess_buffer,
                    uint8_t* output_device_l,
                    uint8_t* output_device_m,
                    cudaStream_t &stream);

    void _split_left(uint8_t *src, 
                     int src_width, int src_height, 
                     uint8_t* dst_l, uint8_t* dst_m,
                     cudaStream_t &stream);

    void _split_right(uint8_t *src, 
                      int src_width, int src_height, 
                      uint8_t* dst_m, 
                      cudaStream_t &stream);

protected:
    /* calc affine matrix  and its reverse */
    void _compute_affine_matrix(const std::vector<size_t> &src,
                                const std::vector<size_t> &dst);

    void _warp_affine_bilinear_and_normalize_plane(uint8_t *src, 
                                                  int src_line_size, 
                                                  int src_width,
                                                  int src_height,
                                                  int dst_width, 
                                                  int dst_height, 
                                                  float *dst, 
                                                  float *matrix_2_3,
                                                  uint8_t padding_value, 
                                                  bool is_cls,
                                                  cudaStream_t &stream);
private:
    inline int __upbound(const size_t n) {
        return (n + __mem_align - 1) / __mem_align * __mem_align; 
    }

public:
    bool _visualize_affine;
    cv::Mat _output_lr;
    cv::Mat _output_m;

protected:
    float _M[9];    // Affine Matrix
    float _M_i[9];  // inverse of Affine Matrix

    const float _left_alpha;
    const float _left_beta;
    const float _right_alpha;
    const float _right_beta;

    // [width, height, channel]
    //std::vector<size_t> _dst_dims;
    int _img_width, _img_height;
    int _left_min, _left_max;
    int _middle_min, _middle_max;
    int _right_min, _right_max;

private:
    const size_t __mem_align;
    const int __max_block_size;
};

} // namespace yolo
#endif
