#ifndef __PREP_HPP__
#define __PREP_HPP__

#include "slice_preprocess.hpp"
#include "cp.hpp"
#include "slice_memory.hpp"

namespace prep{

class PrepImpl {
public:
    PrepImpl(float left_alpha, float left_beta,
             float right_alpha, float right_beta,
             int img_width, int img_height,
             int left_min, int left_max,
             int middle_min, int middle_max,
             int right_min, int right_max,
             int max_batch_size,
             int mem_align,
             int max_block_size,
             const std::string& save_path);

    virtual ~PrepImpl() = default;

    virtual std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> forwards(const std::vector<slice::Image> &images, 
                                                                                  cudaStream_t &stream);
private:
    void __malloc_memory();

public:
    std::vector<std::shared_ptr<slice::Memory<uint8_t>>> preprocess_buffers_;
    slice::Memory<uint8_t> output_buffer_;

    int _img_width, _img_height;
    int _left_min, _left_max;
    int _middle_min, _middle_max;
    int _right_min, _right_max;
    size_t _lr_output_size;
    size_t _middle_output_size;
    size_t _output_size;

private:
    int __max_batch_size;
    slice::Preprocess _prep;
    const std::string _save_path;
};

} // namespace prep

#endif  // __YOLO_HPP__

