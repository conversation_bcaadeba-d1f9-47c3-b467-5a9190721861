#include "prep.hpp"

namespace prep {

/* PrepImpl */
PrepImpl::PrepImpl(float left_alpha, float left_beta,
                   float right_alpha, float right_beta,
                   int img_width, int img_height,
                   int left_min, int left_max,
                   int middle_min, int middle_max,
                   int right_min, int right_max,
                   int max_batch_size,
                   int mem_align,
                   int max_block_size,
                   const std::string& save_path):
    _save_path(save_path),
    __max_batch_size(max_batch_size),
    _img_width(img_width), _img_height(img_height),
    _left_min(left_min), _left_max(left_max),
    _middle_min(middle_min), _middle_max(middle_max),
    _right_min(right_min), _right_max(right_max),
    _prep(slice::Preprocess(left_alpha, left_beta,
                           right_alpha, right_beta,
                           img_width, img_height, 
                           left_min, left_max, 
                           middle_min, middle_max,
                           right_min, right_max,
                           mem_align, max_block_size, true)) {

    __malloc_memory();
}

void PrepImpl::__malloc_memory() {

    //_lr_output_size = (_left_max-_left_min+1)*_img_height;
    //_middle_output_size = (_middle_max-_middle_min+1)*_img_height;
    // output here is 3 channels, awkward...
    _lr_output_size = (_left_max-_left_min)*_img_height*3;
    _middle_output_size = (_middle_max-_middle_min)*_img_height*3;
    _output_size = _lr_output_size + _middle_output_size;

    // slice output  
    output_buffer_.gpu(__max_batch_size*_output_size);

    /* preprocess_buffers_ stores */
    for (int i = 0; i < __max_batch_size; ++i)
        preprocess_buffers_.push_back(std::make_shared<slice::Memory<uint8_t>>());
}

std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> PrepImpl::forwards(const std::vector<slice::Image> &images, 
                                                                                   cudaStream_t &stream) {

    int num_imgs = images.size();
    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> output;

    for (int img_idx = 0; img_idx < num_imgs; ++img_idx) {
        uint8_t* output_device_l; 
        uint8_t* output_device_m;
        output_device_l = output_buffer_.gpu()+img_idx*_output_size;
        output_device_m = output_device_l + _lr_output_size;

        if(images[img_idx].width != _img_width || images[img_idx].height != _img_height) {
            printf("width=%ld, img_width=%d, height=%ld, img_height=%d\n", 
                    images[img_idx].width, _img_width, images[img_idx].height, _img_height);
            printf("image width or height does not match input width or height\n");
            return {};
        }

        _prep.preprocess(images[img_idx],
                         preprocess_buffers_[img_idx],
                         output_device_l,
                         output_device_m,
                         stream);

        checkRuntime(cudaStreamSynchronize(stream));

        // get image name
        //std::string path = images[img_idx].image_path;
        //size_t position = path.find_last_of("/\\");
        //std::string img_name = path.substr(position+1, path.length()-position-1);

        // left: output[0]; right: output[1]
        cv::Mat mat;
        if(images[img_idx].left) {
            _prep._output_lr.convertTo(mat, CV_8UC3);
            output.emplace_back(_prep._output_lr, slice::SliceType::Left, 
                                 images[img_idx].image_name);

            _prep._output_m.convertTo(mat, CV_8UC3);
            output.emplace_back(_prep._output_m, slice::SliceType::Middle, 
                                 images[img_idx].image_name);
        } else {
            _prep._output_lr.convertTo(mat, CV_8UC3);
            output.emplace_back(_prep._output_lr, slice::SliceType::Right, 
                                 images[img_idx].image_name);
        }
    }

    return output;
}

} // namespace prep

