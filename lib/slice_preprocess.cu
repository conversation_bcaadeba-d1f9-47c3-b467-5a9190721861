#include "slice_preprocess.hpp"
#include "slice_log.hpp"
//#include "norm.hpp"

namespace slice {

static __host__ __device__ void affine_project(float *matrix, 
                                               int x, int y, 
                                               float *src_x, float *src_y) {
  *src_x = matrix[0] * x + matrix[1] * y + matrix[2];
  *src_y = matrix[3] * x + matrix[4] * y + matrix[5];
}

static __device__ void normalize(float alpha, float beta,
                                 float *c0, float *c1, float *c2) {

    *c0 = (*c0) * alpha + beta;
    *c1 = (*c1) * alpha + beta;
    *c2 = (*c2) * alpha + beta;
}

// Bilinear Interpolation: INTER_AREA
static __device__ void bilinear_interpolation_inter_area(uint8_t *src,
                                                         int src_line_size,
                                                         float src_x, float src_y, 
                                                         int src_width, int src_height,
                                                         uint8_t padding_value,
                                                         float *c0,
                                                         float *c1,
                                                         float *c2) {

    uint8_t padding_pixel[] = {padding_value, padding_value, padding_value};

    int x_low = floorf(src_x);
    int y_low = floorf(src_y);
    int x_high = x_low + 1;
    int y_high = y_low + 1;

    float ly = src_y - y_low;
    float lx = src_x - x_low;
    float hy = 1 - ly;
    float hx = 1 - lx;

    float w1 = hy * hx, w2 = hy * lx, w3 = ly * hx, w4 = ly * lx;

    uint8_t *v1 = padding_pixel;
    uint8_t *v2 = padding_pixel;
    uint8_t *v3 = padding_pixel;
    uint8_t *v4 = padding_pixel;

    if (y_low >= 0) {
      if (x_low >= 0) v1 = src + y_low * src_line_size + x_low * 3;
      if (x_high < src_width) v2 = src + y_low * src_line_size + x_high * 3;
    }

    if (y_high < src_height) {
      if (x_low >= 0) v3 = src + y_high * src_line_size + x_low * 3;
      if (x_high < src_width) v4 = src + y_high * src_line_size + x_high * 3;
    }

    // same to opencv (w1+w2+w3+w4=1)
    *c0 = floorf(w1 * v1[0] + w2 * v2[0] + w3 * v3[0] + w4 * v4[0] + 0.5f);
    *c1 = floorf(w1 * v1[1] + w2 * v2[1] + w3 * v3[1] + w4 * v4[1] + 0.5f);
    *c2 = floorf(w1 * v1[2] + w2 * v2[2] + w3 * v3[2] + w4 * v4[2] + 0.5f);
}

/* [min, max) */
static __device__ bool check_scope(int x, int min, int max) {
  if(x>=min && x<max) return true;
  return false; 
}

static __global__ void split_left_kernel(uint8_t *src, 
                                  uint8_t* dst_l, uint8_t* dst_m,
                                  int src_width, int src_height, 
                                  int left_min, int left_max, 
                                  int middle_min, int middle_max,
                                  int l_width, int m_width, 
                                  float alpha, float beta) {

    int warp_id = threadIdx.x>>5;
    int lane_id = threadIdx.x&31;

    int warp_row = blockIdx.x*HEIGHT_PER_BLOCK+ROWS_PER_WARP*warp_id;
    int warp_offset = warp_row*src_width;
    uint8_t* warp_ptr = src + warp_offset;

    int img_offset = src_width*src_height;
    int l_img_offset = src_height*l_width;
    int m_img_offset = src_height*m_width;

    int left_offset = warp_row*l_width*3;
    uint8_t* left_ptr = dst_l + left_offset;

    int middle_offset = warp_row*m_width*3;
    uint8_t* mid_ptr = dst_m + middle_offset;

    int tile_n = (src_width+warpSize-1)/warpSize;

    #pragma unroll
    for(int i=0; i<tile_n; ++i) {
      int col = i*warpSize+lane_id;

      if(col>=left_min && col<middle_max) {
        #pragma unroll
        for(int row=0; row<ROWS_PER_WARP; ++row) {
          uint8_t u_element = *(warp_ptr+row*src_width+col);
          float element = static_cast<float>(u_element)*alpha+beta;
          //if(element>255.0) printf("eeeeeeeeeeeelment=%f\n", element);
          element = element>255.0f?255.0:element;
          u_element = static_cast<uint8_t>(element);
          if(check_scope(col, left_min, left_max)) {
            *(left_ptr+(row*l_width+col-left_min)*3) = u_element;
            *(left_ptr+(row*l_width+col-left_min)*3+1) = u_element;
            *(left_ptr+(row*l_width+col-left_min)*3+2) = u_element;
          }
          if(check_scope(col, middle_min, middle_max)) {
            *(mid_ptr+(row*m_width+col-middle_min)*3) = u_element;
            *(mid_ptr+(row*m_width+col-middle_min)*3+1) = u_element;
            *(mid_ptr+(row*m_width+col-middle_min)*3+2) = u_element;
          }
        }
      }
    }

    __syncthreads();

    //if(blockIdx.x==0 && warp_id==0 && lane_id==0) {
    //  for(int i=0; i<l_width; ++i)
    //    for(int j=0; j<src_height; ++j){
    //      printf("dst_l[%d][%d]=%f\n", i,j,*(dst_l+i*l_width+j));

    //    }
    //}

    //int dx = blockDim.x*blockIdx.x+threadIdx.x;
    //int dy = blockDim.y*blockIdx.y+threadIdx.y;

    //if (dx >= src_width || dy >= src_height) return;

    //float src_x = 0., src_y = 0.;
    //float c0=padding_value, c1=padding_value, c2=padding_value;

    /* calc corresponding coord */
    //affine_project(warp_affine_matrix, 
    //               dx, dy, 
    //               &src_x, &src_y);

    //bool x_scope = src_x < -1 || src_x >= src_width;
    //bool y_scope = src_y < -1 || src_y >= src_height;

    //if((!x_scope) && (!y_scope)) {
    //    bilinear_interpolation_inter_area(src,
    //                                      src_line_size,
    //                                      src_x, src_y, 
    //                                      src_width, src_height,
    //                                      padding_value,
    //                                      &c0, &c1, &c2);
    //}

    // visualize only for debug
    //if(visualize) {
    //    float *pdst = dst + dy * dst_width*3 + dx * 3;
    //    pdst[0] = c0; pdst[1] = c1; pdst[2] = c2;
    //    return;
    //}

    /* NOTE: normalize swap R and B channel */
    /* cv::imread(img) is bgr; bgr --> rgb */
    //float t = c2; c2 = c0; c0 = t;
    //normalize(is_cls, 1/255.0f, 0.f, &c0, &c1, &c2);

    /*
     calc offset
     the original data after cv::imread is as follows:
        [bgr_Pixel (0, 0)] [bgr_Pixel (0, 1)] [bgr_Pixel (0, 2)] ... [bgr_Pixel (0, w-1)]
        [bgr_Pixel (1, 0)] [bgr_Pixel (1, 1)] [bgr_Pixel (1, 2)] ... [bgr_Pixel (1, w-1)]
        ...
        [bgr_Pixel (h-1, 0)] [bgr_Pixel (h-1, 1)] [bgr_Pixel (h-1, 2)] ... [bgr_Pixel (h-1, w-1)]

        NOTE: Each bgr_pixel is represented by three consecutive bytes, 
              which correspond to the Blue (B), Green (G), and Red (R) channels, respectively.

     after calc offset (and swap R and B channel in normalize), it will be as follows:
       r channel:
        [r_Pixel (0, 0)] [r_Pixel (0, 1)] [r_Pixel (0, 2)] ... [r_Pixel (0, w-1)]
        [r_Pixel (1, 0)] [r_Pixel (1, 1)] [r_Pixel (1, 2)] ... [r_Pixel (1, w-1)]
        ...
        [r_Pixel (h-1, 0)] [r_Pixel (h-1, 1)] [r_Pixel (h-1, 2)] ... [r_Pixel (h-1, w-1)]

       g channel:
        [g_Pixel (0, 0)] [g_Pixel (0, 1)] [g_Pixel (0, 2)] ... [g_Pixel (0, w-1)]
        [g_Pixel (1, 0)] [g_Pixel (1, 1)] [g_Pixel (1, 2)] ... [g_Pixel (1, w-1)]
        ...
        [g_Pixel (h-1, 0)] [g_Pixel (h-1, 1)] [g_Pixel (h-1, 2)] ... [g_Pixel (h-1, w-1)]

       b channel:
        [b_Pixel (0, 0)] [b_Pixel (0, 1)] [b_Pixel (0, 2)] ... [b_Pixel (0, w-1)]
        [b_Pixel (1, 0)] [b_Pixel (1, 1)] [b_Pixel (1, 2)] ... [b_Pixel (1, w-1)]
        ...
        [b_Pixel (h-1, 0)] [b_Pixel (h-1, 1)] [b_Pixel (h-1, 2)] ... [b_Pixel (h-1, w-1)]

     */
    //int area = dst_width * dst_height;
    //float *pdst_c0 = dst + dy * dst_width + dx;
    //float *pdst_c1 = pdst_c0 + area;
    //float *pdst_c2 = pdst_c1 + area;

    /* assign pixel value */
    //*pdst_c0 = c0;
    //*pdst_c1 = c1;
    //*pdst_c2 = c2;
}

static __global__ void split_right_kernel(uint8_t *src, uint8_t* dst_r, 
                                  int src_width, int src_height, 
                                  int right_min, int right_max, 
                                  int r_width, 
                                  float alpha, float beta) {


    int warp_id = threadIdx.x>>5;
    int lane_id = threadIdx.x&31;

    int warp_row = blockIdx.x*HEIGHT_PER_BLOCK+ROWS_PER_WARP*warp_id;
    int warp_offset = warp_row*src_width;
    uint8_t* warp_ptr = src + warp_offset;

    int img_offset = src_height*r_width;

    int right_offset = warp_row*r_width*3;
    uint8_t* right_ptr = dst_r + right_offset;

    int tile_n = (src_width+warpSize-1)/warpSize;

    #pragma unroll
    for(int i=0; i<tile_n; ++i) {
      int col = i*warpSize+lane_id;

      if(col>=right_min && col<right_max) {
        #pragma unroll
        for(int row=0; row<ROWS_PER_WARP; ++row) {
          uint8_t u_element = *(warp_ptr+row*src_width+col);
          float element = static_cast<float>(u_element)*alpha+beta;
          element = element>255.0f?255.0:element;
          u_element = static_cast<uint8_t>(element);
          if(check_scope(col, right_min, right_max)) {
            *(right_ptr+(row*r_width+col-right_min)*3) = u_element;
            *(right_ptr+(row*r_width+col-right_min)*3+1) = u_element;
            *(right_ptr+(row*r_width+col-right_min)*3+2) = u_element;
          }
        }
      }
    }

    __syncthreads();
}

/*
    src: original img width & height
    dst: trget img width & height
 */
//void Preprocess::_compute_affine_matrix(const std::vector<size_t> &src,
//                                        const std::vector<size_t> &dst) {
//
//    float scale_x = static_cast<float>(dst[0])/src[0];
//    float scale_y = static_cast<float>(dst[1])/src[1];
//    float scale = std::min(scale_x, scale_y);
//
//    auto trans = [&](float d1, float d2) {
//        return 0.5*(d1-d2*scale);
//    };
//
//    float ox = trans(static_cast<float>(dst[0]), static_cast<float>(src[0]));
//    float oy = trans(static_cast<float>(dst[1]), static_cast<float>(src[1]));
//
//    /* Affine Matrix */
//    _M[0] = scale; _M[1] = 0; _M[2] = ox + 0.5*(scale-1);
//    _M[3] = 0; _M[4] = scale; _M[5] = oy + 0.5*(scale-1);
//    _M[6] = 0; _M[7] = 0; _M[8] = 1;
//
//    /* inverse of Affine Matrix */
//    _M_i[0] = 1./scale; _M_i[1] = 0.;       _M_i[2] = -ox/scale;
//    _M_i[3] = 0;        _M_i[4] = 1./scale; _M_i[5] = -oy/scale;
//    _M_i[6] = 0.;       _M_i[7] = 0.;       _M_i[8] = 1.;
//}

void Preprocess::preprocess(const Image &image,
                            std::shared_ptr<Memory<uint8_t>> preprocess_buffer,
                            uint8_t* output_device_lr,
                            uint8_t* output_device_m,
                            cudaStream_t &stream) {

    // avoid emtpy image
    if(image.bgrptr == nullptr) return;

    // channel is 1 for gray img
    size_t image_size = image.width*image.height;

    uint8_t *gpu_workspace = preprocess_buffer->gpu(image_size);
    uint8_t *cpu_workspace = preprocess_buffer->cpu(image_size);

    // speed up
    memcpy(cpu_workspace, image.bgrptr, image_size);

    checkRuntime(cudaMemcpyAsync(gpu_workspace, 
                                 cpu_workspace, 
                                 image_size,
                                 cudaMemcpyHostToDevice, 
                                 stream));

    if(image.left) {
      _split_left(gpu_workspace, image.width, image.height, 
                  output_device_lr, output_device_m, stream);
    } else {
      _split_right(gpu_workspace, image.width, image.height,
                   output_device_lr, stream);
    }

    _output_lr = cv::Mat(cv::Size((_left_max-_left_min), _img_height), CV_8UC3);
    size_t lr_output_size = (_left_max-_left_min)*_img_height*3*sizeof(uint8_t);
    checkRuntime(cudaMemcpyAsync(_output_lr.data, 
                                 output_device_lr, 
                                 lr_output_size,
                                 cudaMemcpyDeviceToHost, 
                                 stream));

    if(image.left) {
      //_output_m = cv::Mat(cv::Size((_middle_max-_middle_min), _img_height), CV_32FC3);
      _output_m = cv::Mat(cv::Size((_middle_max-_middle_min), _img_height), CV_8UC3);
      size_t middle_output_size = (_middle_max-_middle_min)*_img_height*3*sizeof(uint8_t);
      checkRuntime(cudaMemcpyAsync(_output_m.data, 
                                   output_device_m, 
                                   middle_output_size,
                                   cudaMemcpyDeviceToHost, 
                                   stream));
    }
}

void Preprocess::_split_left(uint8_t *src, 
                             int src_width, int src_height, 
                             uint8_t* dst_l, uint8_t* dst_m, 
                             cudaStream_t &stream) {

    int l_width = _left_max-_left_min;
    int m_width = _middle_max-_middle_min;

    /* every block deals with 16*src_width*channel */
    dim3 grid((src_height + HEIGHT_PER_BLOCK-1)/HEIGHT_PER_BLOCK);
    dim3 block(THREADS_PER_BLOCK);

    checkKernel(
        split_left_kernel<<<grid, block, 0, stream>>>(
        src, dst_l, dst_m, src_width, src_height, 
        _left_min, _left_max, _middle_min, _middle_max,
        l_width, m_width, _left_alpha, _left_beta));
}

void Preprocess::_split_right(uint8_t *src, 
                             int src_width, int src_height, 
                             uint8_t* dst_r, 
                             cudaStream_t &stream) {

    int r_width = _right_max-_right_min;

    /* every block deals with 16*src_width */
    dim3 grid((src_height + HEIGHT_PER_BLOCK-1)/HEIGHT_PER_BLOCK);
    dim3 block(THREADS_PER_BLOCK);

    checkKernel(
        split_right_kernel<<<grid, block, 0, stream>>>(
        src, dst_r, src_width, src_height, 
        _right_min, _right_max, 
        r_width, _right_alpha, _right_beta));
}

} // namespace slice

