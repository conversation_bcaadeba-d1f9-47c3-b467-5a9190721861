#include "preprocess_infer.hpp"

namespace prep {

bool Slice::multi_thread_start() {

    bool ok = _cpm_infer.start(__prep_infer, 
                              __stream,
                              __micro_sec_laytency,
                              __max_batch_size, 
                              __max_queue_imgs);

    if (!ok) {
        std::cout << "thread start failed" << std::endl;
        return false;
    }

    return true;
}

std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>>
Slice::multi_thread_infer(std::queue<std::pair<bool, std::string>> &files) {
    auto ret = _cpm_infer.commits(files);

    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> output;
    for(auto& r: ret) {
        auto tmp = r.get();
        for(auto& o: tmp.val) {
            output.emplace_back(o);
        }
    }

    return output;
}

std::string Slice::__get_image_name(const std::string &path) {
    // '/' for linux ; '\\' for windows
    size_t position = path.find_last_of("/\\");

    // get the last split string
    return path.substr(position+1, path.length()-position-1);
}

//Slice::single_worker(std::queue<std::pair<bool, std::string>>& files) {
std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>>
Slice::single_worker(std::tuple<bool, cv::Mat, std::string>& data) {

    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> output;

    bool left = std::get<0>(data);
    cv::Mat mat = std::get<1>(data);
    std::string img_name = std::get<2>(data);

    // avoid emtpy image
    if(mat.empty()) {
        mat.data = nullptr;
        mat.rows = 0;
        mat.cols= 0;
    }

    std::vector<slice::Image> inputs = {slice::Image(mat.data, mat.cols, mat.rows, left, img_name)};
    std::vector<std::tuple<cv::Mat, slice::SliceType, std::string>> ret = 
                                    __prep_infer->forwards(inputs, __stream);
    for(auto& r: ret) output.emplace_back(r);

    return output;
}

} // namespace prep

