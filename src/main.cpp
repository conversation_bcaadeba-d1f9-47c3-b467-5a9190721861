#include "common.hpp"
#include "slice_preprocess.hpp"
#include "prep_test.hpp"

int main () {
    const std::string image_path = "/home/<USER>/workspace/tmp/shanghai/test/上海地铁10号线_上行_0_K0+000_Y_Y_2025-03-05-01-01-48"
    const std::string save_path = "/home/<USER>/workspace/tmp/splits/results"; 

    int mem_align = 32;
    int max_block_size = 256;
    int max_batch_size = 8;
    int max_queue_imgs = 160;

    float left_alpha = 1.0+120/100; 
    float left_beta = 15;

    float right_alpha = 1.0+100/100; 
    float right_beta = 10;

    int img_width = 3496, img_height = 2000;

    /* [min, max) ==> width = max-min */
    int left_min = 70;
    int left_max = left_min+2060;
    int middle_min = 2059;
    int middle_max = middle_min+1437;
    int right_min = 1436;
    int right_max = right_min+2060;

    SliceTest prt(image_path, save_path,
                  left_alpha, left_beta,
                  right_alpha, right_beta,
                  img_width, img_height,
                  left_min, left_max,
                  middle_min, middle_max,
                  right_min, right_max,
                  mem_align,
                  max_block_size,
                  max_batch_size,
                  max_queue_imgs);

    /* multi-thread infer, read image in lib */
    prt.single_img_slice();
}

