#include "prep_test.hpp"

#ifdef WIN32
void SliceTest::__get_files(const std::string& path, bool left) {
    intptr_t handle;
    struct _finddata_t fileInfo;

    handle = _findfirst((path + "\\*").c_str(), &fileInfo);
    if (handle == -1) {
        std::cerr << "Error finding files in directory: " << 
                     path.c_str() << std::endl;
        return;
    }

    do {
        if (!(fileInfo.attrib & _A_SUBDIR)) {
            std::string img_p = path;
            if(left)
                __l_files.push(img_p.append("\\").append(fileInfo.name));
            else
                __r_files.push(img_p.append("\\").append(fileInfo.name));
        }
    } while (_findnext(handle, &fileInfo) == 0);

    _findclose(handle);
}

#else

void SliceTest::__get_files(const std::string &path, bool left) {
    DIR *dir;
    struct dirent *ptr;
    if ((dir = opendir(path.c_str())) == NULL)
    {
        perror("Open dir error...");
        return;
    }

    while ((ptr = readdir(dir)) != NULL)
    {
        // current dir or parent dir
        if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0) {
            continue;
        } else if (ptr->d_type == 8) {
            std::string strFile;
            strFile = path;
            strFile += "/";
            strFile += ptr->d_name;
            if(left) __l_files.push(strFile);
            else __r_files.push(strFile);
        }
        else
            continue;
    }
    closedir(dir);
    return;
}
#endif // system

//int SliceTest::thread_img_path() {
//
//    std::string l_path = _image_path+"/L";
//    std::string r_path = _image_path+"/R";
//
//    /* step1: read images */
//    __get_files(l_path, true);
//    int l_num_files = __l_files.size();
//    __get_files(r_path, false);
//    int r_num_files = __r_files.size();
//
//    /* step2: start thread */
//    std::cout << "start process" << std::endl;
//    if(!__infer->multi_thread_start()){
//        std::cout << "multi thread start failed" << std::endl;
//        return -1;
//    }
//
//    /* step3: inference */
//    int l_imgs_left = l_num_files;
//    int r_imgs_left = r_num_files;
//    int rand = 0;
//    std::queue<std::pair<bool, std::string>> sub_files;
//
//    auto start = std::chrono::high_resolution_clock::now();
//    while(!__l_files.empty()) {
//
//        if(l_imgs_left < MAX_RANDOM) rand = l_imgs_left;
//        else rand = MAX_RANDOM;
//
//        for (int i = 0; i < rand; ++i) {
//            auto path = __l_files.front();
//            //sub_files.push(path);
//            sub_files.push(std::make_pair(true, path));
//            __l_files.pop();
//        }
//
//        /* NOTE: inference here */
//        auto ret = __infer->multi_thread_infer(sub_files);
//        l_imgs_left -= rand;
//
//        /* write imgs to disk */
//        for(auto& r: ret) {
//            cv::Mat mat = std::get<0>(r);
//            slice::SliceType st = std::get<1>(r);
//            std::string img_name = std::get<2>(r);
//
//            std::string img_path;
//            switch (st) {
//                case slice::SliceType::Left:
//                    img_path = _save_path+"/T-L/"+img_name;
//                    cv::imwrite(img_path, mat);
//                    break;
//                case slice::SliceType::Middle:
//                    img_path = _save_path+"/T-M/"+img_name;
//                    cv::imwrite(img_path, mat);
//                    break;
//                case slice::SliceType::Right:
//                    img_path = _save_path+"/T-R/"+img_name;
//                    cv::imwrite(img_path, mat);
//                    break;
//                default:
//                    printf("SliceType err");
//                    break;
//            }
//        }
//    }
//
//    while(!__r_files.empty()) {
//
//        if(r_imgs_left < MAX_RANDOM) rand = r_imgs_left;
//        else rand = MAX_RANDOM;
//
//        for (int i = 0; i < rand; ++i) {
//            auto path = __r_files.front();
//            sub_files.push(std::make_pair(false, path));
//            __r_files.pop();
//        }
//
//        /* NOTE: inference here */
//        auto ret = __infer->multi_thread_infer(sub_files);
//        r_imgs_left -= rand;
//
//        /* write imgs to disk */
//        for(auto& r: ret) {
//            cv::Mat mat = std::get<0>(r);
//            slice::SliceType st = std::get<1>(r);
//            std::string img_name = std::get<2>(r);
//
//            std::string img_path;
//            switch (st) {
//                case slice::SliceType::Left:
//                    img_path = _save_path+"/T-L/"+img_name;
//                    cv::imwrite(img_path, mat);
//                    break;
//                case slice::SliceType::Middle:
//                    img_path = _save_path+"/T-M/"+img_name;
//                    cv::imwrite(img_path, mat);
//                    break;
//                case slice::SliceType::Right:
//                    img_path = _save_path+"/T-R/"+img_name;
//                    cv::imwrite(img_path, mat);
//                    break;
//                default:
//                    printf("SliceType err");
//                    break;
//            }
//        }
//    }
//
//    auto end = std::chrono::high_resolution_clock::now();
//    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
//    float t = static_cast<float>(duration.count());
//    printf("%f ms per img\n", t/(l_num_files+r_num_files)/1000);
//
//    return 0;
//}

int SliceTest::single_img_slice() {

    std::string l_path = _image_path+"/L";
    std::string r_path = _image_path+"/R";

    /* step1: read images */
    __get_files(l_path, true);
    int l_num_files = __l_files.size();

    printf("ssssss, l_path: %s, num=%lu\n", l_path.c_str(), __l_files.size());
    for(int i=0; i<l_num_files; ++i) {
        std::string img_name = __l_files.front();
        printf("i=%d, num=%lu, %s\n", i, __l_files.size(), img_name.c_str());
    }

    __get_files(r_path, false);
    int r_num_files = __r_files.size();

    printf("ssssss,r_path: %s, num=%lu\n", r_path.c_str(), __r_files.size());
    for(int i=0; i<r_num_files; ++i) {
        std::string img_name = __r_files.front();
        printf("i=%d, num=%lu, %s\n", i, __r_files.size(), img_name.c_str());
    }


    /* step3: inference */
    auto start = std::chrono::high_resolution_clock::now();
    while(!__l_files.empty()) {

        auto path = __l_files.front();
        cv::Mat mat = cv::imread(path, cv::IMREAD_GRAYSCALE);

        size_t position = path.find_last_of("/\\");
        std::string img_name = path.substr(position+1, path.length()-position-1);

        std::tuple<bool, cv::Mat, std::string> data = std::make_tuple(true, mat, img_name);
        __l_files.pop();

        /* NOTE: inference here */
        auto ret = __infer->single_worker(data);

        /* write imgs to disk */
        for(auto& r: ret) {
            cv::Mat mat = std::get<0>(r);
            slice::SliceType st = std::get<1>(r);
            std::string img_name = std::get<2>(r);

            std::string img_path;
            switch (st) {
                case slice::SliceType::Left:
                    img_path = _save_path+"/T-L/"+img_name;
                    cv::imwrite(img_path, mat);
                    break;
                case slice::SliceType::Middle:
                    img_path = _save_path+"/T-M/"+img_name;
                    cv::imwrite(img_path, mat);
                    break;
                case slice::SliceType::Right:
                    img_path = _save_path+"/T-R/"+img_name;
                    cv::imwrite(img_path, mat);
                    break;
                default:
                    printf("SliceType err");
                    break;
            }
        }
    }

    while(!__r_files.empty()) {

        auto path = __r_files.front();
        cv::Mat mat = cv::imread(path, cv::IMREAD_GRAYSCALE);

        size_t position = path.find_last_of("/\\");
        std::string img_name = path.substr(position+1, path.length()-position-1);

        std::tuple<bool, cv::Mat, std::string> data = std::make_tuple(false, mat, img_name);
        __r_files.pop();

        /* NOTE: inference here */
        auto ret = __infer->single_worker(data);

        std::string img_path;
        /* write imgs to disk */
        for(auto& r: ret) {
            cv::Mat mat = std::get<0>(r);
            slice::SliceType st = std::get<1>(r);
            std::string img_name = std::get<2>(r);

            switch (st) {
                case slice::SliceType::Left:
                    img_path = _save_path+"/T-L/"+img_name;
                    cv::imwrite(img_path, mat);
                    break;
                case slice::SliceType::Middle:
                    img_path = _save_path+"/T-M/"+img_name;
                    cv::imwrite(img_path, mat);
                    break;
                case slice::SliceType::Right:
                    img_path = _save_path+"/T-R/"+img_name;
                    cv::imwrite(img_path, mat);
                    break;
                default:
                    printf("SliceType err");
                    break;
            }
        }
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    float t = static_cast<float>(duration.count());
    printf("%f ms per img\n", t/(l_num_files+r_num_files)/1000);

    return 0;
}
