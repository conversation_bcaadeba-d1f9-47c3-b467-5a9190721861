# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/miniconda3/lib/python3.8/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /home/<USER>/miniconda3/lib/python3.8/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build

# Include any dependencies generated for this target.
include CMakeFiles/parse_cfg.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/parse_cfg.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/parse_cfg.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/parse_cfg.dir/flags.make

CMakeFiles/parse_cfg.dir/lib/common.c.o: CMakeFiles/parse_cfg.dir/flags.make
CMakeFiles/parse_cfg.dir/lib/common.c.o: /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/common.c
CMakeFiles/parse_cfg.dir/lib/common.c.o: CMakeFiles/parse_cfg.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/parse_cfg.dir/lib/common.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/parse_cfg.dir/lib/common.c.o -MF CMakeFiles/parse_cfg.dir/lib/common.c.o.d -o CMakeFiles/parse_cfg.dir/lib/common.c.o -c /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/common.c

CMakeFiles/parse_cfg.dir/lib/common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/parse_cfg.dir/lib/common.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/common.c > CMakeFiles/parse_cfg.dir/lib/common.c.i

CMakeFiles/parse_cfg.dir/lib/common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/parse_cfg.dir/lib/common.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/common.c -o CMakeFiles/parse_cfg.dir/lib/common.c.s

CMakeFiles/parse_cfg.dir/lib/io.c.o: CMakeFiles/parse_cfg.dir/flags.make
CMakeFiles/parse_cfg.dir/lib/io.c.o: /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/io.c
CMakeFiles/parse_cfg.dir/lib/io.c.o: CMakeFiles/parse_cfg.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/parse_cfg.dir/lib/io.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/parse_cfg.dir/lib/io.c.o -MF CMakeFiles/parse_cfg.dir/lib/io.c.o.d -o CMakeFiles/parse_cfg.dir/lib/io.c.o -c /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/io.c

CMakeFiles/parse_cfg.dir/lib/io.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/parse_cfg.dir/lib/io.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/io.c > CMakeFiles/parse_cfg.dir/lib/io.c.i

CMakeFiles/parse_cfg.dir/lib/io.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/parse_cfg.dir/lib/io.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/io.c -o CMakeFiles/parse_cfg.dir/lib/io.c.s

CMakeFiles/parse_cfg.dir/lib/list.c.o: CMakeFiles/parse_cfg.dir/flags.make
CMakeFiles/parse_cfg.dir/lib/list.c.o: /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/list.c
CMakeFiles/parse_cfg.dir/lib/list.c.o: CMakeFiles/parse_cfg.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/parse_cfg.dir/lib/list.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/parse_cfg.dir/lib/list.c.o -MF CMakeFiles/parse_cfg.dir/lib/list.c.o.d -o CMakeFiles/parse_cfg.dir/lib/list.c.o -c /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/list.c

CMakeFiles/parse_cfg.dir/lib/list.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/parse_cfg.dir/lib/list.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/list.c > CMakeFiles/parse_cfg.dir/lib/list.c.i

CMakeFiles/parse_cfg.dir/lib/list.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/parse_cfg.dir/lib/list.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/list.c -o CMakeFiles/parse_cfg.dir/lib/list.c.s

CMakeFiles/parse_cfg.dir/lib/memory.c.o: CMakeFiles/parse_cfg.dir/flags.make
CMakeFiles/parse_cfg.dir/lib/memory.c.o: /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/memory.c
CMakeFiles/parse_cfg.dir/lib/memory.c.o: CMakeFiles/parse_cfg.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/parse_cfg.dir/lib/memory.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/parse_cfg.dir/lib/memory.c.o -MF CMakeFiles/parse_cfg.dir/lib/memory.c.o.d -o CMakeFiles/parse_cfg.dir/lib/memory.c.o -c /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/memory.c

CMakeFiles/parse_cfg.dir/lib/memory.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/parse_cfg.dir/lib/memory.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/memory.c > CMakeFiles/parse_cfg.dir/lib/memory.c.i

CMakeFiles/parse_cfg.dir/lib/memory.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/parse_cfg.dir/lib/memory.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/memory.c -o CMakeFiles/parse_cfg.dir/lib/memory.c.s

CMakeFiles/parse_cfg.dir/lib/parser.c.o: CMakeFiles/parse_cfg.dir/flags.make
CMakeFiles/parse_cfg.dir/lib/parser.c.o: /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/parser.c
CMakeFiles/parse_cfg.dir/lib/parser.c.o: CMakeFiles/parse_cfg.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/parse_cfg.dir/lib/parser.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/parse_cfg.dir/lib/parser.c.o -MF CMakeFiles/parse_cfg.dir/lib/parser.c.o.d -o CMakeFiles/parse_cfg.dir/lib/parser.c.o -c /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/parser.c

CMakeFiles/parse_cfg.dir/lib/parser.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/parse_cfg.dir/lib/parser.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/parser.c > CMakeFiles/parse_cfg.dir/lib/parser.c.i

CMakeFiles/parse_cfg.dir/lib/parser.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/parse_cfg.dir/lib/parser.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/parser.c -o CMakeFiles/parse_cfg.dir/lib/parser.c.s

CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o: CMakeFiles/parse_cfg.dir/flags.make
CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o: /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/read_cfg.c
CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o: CMakeFiles/parse_cfg.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o -MF CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o.d -o CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o -c /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/read_cfg.c

CMakeFiles/parse_cfg.dir/lib/read_cfg.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/parse_cfg.dir/lib/read_cfg.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/read_cfg.c > CMakeFiles/parse_cfg.dir/lib/read_cfg.c.i

CMakeFiles/parse_cfg.dir/lib/read_cfg.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/parse_cfg.dir/lib/read_cfg.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/read_cfg.c -o CMakeFiles/parse_cfg.dir/lib/read_cfg.c.s

# Object files for target parse_cfg
parse_cfg_OBJECTS = \
"CMakeFiles/parse_cfg.dir/lib/common.c.o" \
"CMakeFiles/parse_cfg.dir/lib/io.c.o" \
"CMakeFiles/parse_cfg.dir/lib/list.c.o" \
"CMakeFiles/parse_cfg.dir/lib/memory.c.o" \
"CMakeFiles/parse_cfg.dir/lib/parser.c.o" \
"CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o"

# External object files for target parse_cfg
parse_cfg_EXTERNAL_OBJECTS =

libparse_cfg.so: CMakeFiles/parse_cfg.dir/lib/common.c.o
libparse_cfg.so: CMakeFiles/parse_cfg.dir/lib/io.c.o
libparse_cfg.so: CMakeFiles/parse_cfg.dir/lib/list.c.o
libparse_cfg.so: CMakeFiles/parse_cfg.dir/lib/memory.c.o
libparse_cfg.so: CMakeFiles/parse_cfg.dir/lib/parser.c.o
libparse_cfg.so: CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o
libparse_cfg.so: CMakeFiles/parse_cfg.dir/build.make
libparse_cfg.so: CMakeFiles/parse_cfg.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking C shared library libparse_cfg.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/parse_cfg.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/parse_cfg.dir/build: libparse_cfg.so
.PHONY : CMakeFiles/parse_cfg.dir/build

CMakeFiles/parse_cfg.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/parse_cfg.dir/cmake_clean.cmake
.PHONY : CMakeFiles/parse_cfg.dir/clean

CMakeFiles/parse_cfg.dir/depend:
	cd /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles/parse_cfg.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/parse_cfg.dir/depend

