
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/common.c" "CMakeFiles/parse_cfg.dir/lib/common.c.o" "gcc" "CMakeFiles/parse_cfg.dir/lib/common.c.o.d"
  "/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/io.c" "CMakeFiles/parse_cfg.dir/lib/io.c.o" "gcc" "CMakeFiles/parse_cfg.dir/lib/io.c.o.d"
  "/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/list.c" "CMakeFiles/parse_cfg.dir/lib/list.c.o" "gcc" "CMakeFiles/parse_cfg.dir/lib/list.c.o.d"
  "/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/memory.c" "CMakeFiles/parse_cfg.dir/lib/memory.c.o" "gcc" "CMakeFiles/parse_cfg.dir/lib/memory.c.o.d"
  "/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/parser.c" "CMakeFiles/parse_cfg.dir/lib/parser.c.o" "gcc" "CMakeFiles/parse_cfg.dir/lib/parser.c.o.d"
  "/home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/lib/read_cfg.c" "CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o" "gcc" "CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
