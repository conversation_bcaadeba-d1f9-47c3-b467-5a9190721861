# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/miniconda3/lib/python3.8/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /home/<USER>/miniconda3/lib/python3.8/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/home/<USER>/miniconda3/lib/python3.8/site-packages/cmake/data/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/home/<USER>/miniconda3/lib/python3.8/site-packages/cmake/data/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/workspace/yolov8_all/yolov8_detection/third_party/config_parser/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named parse_cfg

# Build rule for target.
parse_cfg: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 parse_cfg
.PHONY : parse_cfg

# fast build rule for target.
parse_cfg/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/build
.PHONY : parse_cfg/fast

#=============================================================================
# Target rules for targets named test

# Build rule for target.
test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test
.PHONY : test

# fast build rule for target.
test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test.dir/build.make CMakeFiles/test.dir/build
.PHONY : test/fast

lib/common.o: lib/common.c.o
.PHONY : lib/common.o

# target to build an object file
lib/common.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/common.c.o
.PHONY : lib/common.c.o

lib/common.i: lib/common.c.i
.PHONY : lib/common.i

# target to preprocess a source file
lib/common.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/common.c.i
.PHONY : lib/common.c.i

lib/common.s: lib/common.c.s
.PHONY : lib/common.s

# target to generate assembly for a file
lib/common.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/common.c.s
.PHONY : lib/common.c.s

lib/io.o: lib/io.c.o
.PHONY : lib/io.o

# target to build an object file
lib/io.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/io.c.o
.PHONY : lib/io.c.o

lib/io.i: lib/io.c.i
.PHONY : lib/io.i

# target to preprocess a source file
lib/io.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/io.c.i
.PHONY : lib/io.c.i

lib/io.s: lib/io.c.s
.PHONY : lib/io.s

# target to generate assembly for a file
lib/io.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/io.c.s
.PHONY : lib/io.c.s

lib/list.o: lib/list.c.o
.PHONY : lib/list.o

# target to build an object file
lib/list.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/list.c.o
.PHONY : lib/list.c.o

lib/list.i: lib/list.c.i
.PHONY : lib/list.i

# target to preprocess a source file
lib/list.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/list.c.i
.PHONY : lib/list.c.i

lib/list.s: lib/list.c.s
.PHONY : lib/list.s

# target to generate assembly for a file
lib/list.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/list.c.s
.PHONY : lib/list.c.s

lib/memory.o: lib/memory.c.o
.PHONY : lib/memory.o

# target to build an object file
lib/memory.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/memory.c.o
.PHONY : lib/memory.c.o

lib/memory.i: lib/memory.c.i
.PHONY : lib/memory.i

# target to preprocess a source file
lib/memory.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/memory.c.i
.PHONY : lib/memory.c.i

lib/memory.s: lib/memory.c.s
.PHONY : lib/memory.s

# target to generate assembly for a file
lib/memory.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/memory.c.s
.PHONY : lib/memory.c.s

lib/parser.o: lib/parser.c.o
.PHONY : lib/parser.o

# target to build an object file
lib/parser.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/parser.c.o
.PHONY : lib/parser.c.o

lib/parser.i: lib/parser.c.i
.PHONY : lib/parser.i

# target to preprocess a source file
lib/parser.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/parser.c.i
.PHONY : lib/parser.c.i

lib/parser.s: lib/parser.c.s
.PHONY : lib/parser.s

# target to generate assembly for a file
lib/parser.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/parser.c.s
.PHONY : lib/parser.c.s

lib/read_cfg.o: lib/read_cfg.c.o
.PHONY : lib/read_cfg.o

# target to build an object file
lib/read_cfg.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/read_cfg.c.o
.PHONY : lib/read_cfg.c.o

lib/read_cfg.i: lib/read_cfg.c.i
.PHONY : lib/read_cfg.i

# target to preprocess a source file
lib/read_cfg.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/read_cfg.c.i
.PHONY : lib/read_cfg.c.i

lib/read_cfg.s: lib/read_cfg.c.s
.PHONY : lib/read_cfg.s

# target to generate assembly for a file
lib/read_cfg.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/parse_cfg.dir/build.make CMakeFiles/parse_cfg.dir/lib/read_cfg.c.s
.PHONY : lib/read_cfg.c.s

src/test.o: src/test.cpp.o
.PHONY : src/test.o

# target to build an object file
src/test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test.dir/build.make CMakeFiles/test.dir/src/test.cpp.o
.PHONY : src/test.cpp.o

src/test.i: src/test.cpp.i
.PHONY : src/test.i

# target to preprocess a source file
src/test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test.dir/build.make CMakeFiles/test.dir/src/test.cpp.i
.PHONY : src/test.cpp.i

src/test.s: src/test.cpp.s
.PHONY : src/test.s

# target to generate assembly for a file
src/test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test.dir/build.make CMakeFiles/test.dir/src/test.cpp.s
.PHONY : src/test.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... parse_cfg"
	@echo "... test"
	@echo "... lib/common.o"
	@echo "... lib/common.i"
	@echo "... lib/common.s"
	@echo "... lib/io.o"
	@echo "... lib/io.i"
	@echo "... lib/io.s"
	@echo "... lib/list.o"
	@echo "... lib/list.i"
	@echo "... lib/list.s"
	@echo "... lib/memory.o"
	@echo "... lib/memory.i"
	@echo "... lib/memory.s"
	@echo "... lib/parser.o"
	@echo "... lib/parser.i"
	@echo "... lib/parser.s"
	@echo "... lib/read_cfg.o"
	@echo "... lib/read_cfg.i"
	@echo "... lib/read_cfg.s"
	@echo "... src/test.o"
	@echo "... src/test.i"
	@echo "... src/test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

