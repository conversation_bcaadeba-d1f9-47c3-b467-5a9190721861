cmake_minimum_required(VERSION 3.12)

project(test LANGUAGES C CXX)  # set project name

SET(CMAKE_CXX_STANDARD 11)
SET(CMAKE_BUILD_TYPE "Debug")  # 定义编译类型
SET(CMAKE_CXX_FLAGS_DEBUG "$ENV{CXXFLAGS} -O0 -Wall -g2 -ggdb") # 定义Debug编译参数
SET(CMAKE_CXX_FLAGS_RELEASE "$ENV{CXXFLAGS} -O3 -Wall") # 定义Release编译参数

set(SRC "./src/test.cpp")
set(PROJ_HEAD ${PROJECT_SOURCE_DIR}/include)

file(GLOB_RECURSE LIB_SRC
    ${PROJECT_SOURCE_DIR}/lib/*.c)

include_directories(${PROJ_HEAD})
set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
# create a library target
add_library(parse_cfg SHARED ${LIB_SRC})
add_executable(test ${SRC})
target_link_libraries(test PUBLIC parse_cfg)

