[task]
classes = GMDK, GDYC, KJQS, KJWX, KJDL, TTQS, TTSD, TTDL, DBWX, LSQS, GZDK, DCLW, GDYW, JB
#debug_log=0
err_log=1

[preprocess]
visualize_affine=0
show_info=1
max_batch_size=4

[postprocess]
confidence_threshold=0.5
nms_threshold=0.5
num_box_element=8
max_image_bboxes=64

[gpu]
gpu_block_threads=512
max_block_size=32
mem_align=32

[multi_threads]
timer=0
max_queue_imgs=15
micro_sec_laytency=100

