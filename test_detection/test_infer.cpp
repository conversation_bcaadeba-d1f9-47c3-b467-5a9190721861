#include "test_infer.hpp"

#ifdef WIN32
void Test::__get_files(const std::string& path) {
    intptr_t handle;
    struct _finddata_t fileInfo;

    handle = _findfirst((path + "\\*").c_str(), &fileInfo);
    if (handle == -1) {
        std::cerr << "Error finding files in directory: " << 
                     path.c_str() << std::endl;
        return;
    }

    do {
        if (!(fileInfo.attrib & _A_SUBDIR)) {
            std::string img_p = path;
            __files.push(img_p.append("\\").append(fileInfo.name));
        }
    } while (_findnext(handle, &fileInfo) == 0);

    _findclose(handle);
}

#else

void Test::__get_files(const std::string &path) {
    DIR *dir;
    struct dirent *ptr;
    if ((dir = opendir(path.c_str())) == NULL)
    {
        perror("Open dir error...");
        return;
    }

    while ((ptr = readdir(dir)) != NULL)
    {
        // current dir or parent dir
        if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0) {
            continue;
        } else if (ptr->d_type == 8) {
            std::string strFile;
            strFile = path;
            strFile += "/";
            strFile += ptr->d_name;
            __files.push(strFile);
        }
        else
            continue;
    }
    closedir(dir);
    return;
}
#endif // system

void Test::__test_img_read(const std::string &image_path) {

    __get_files(image_path);
    int num_files = __files.size();

    auto start = std::chrono::high_resolution_clock::now();

    while(!__files.empty()) {
        auto path = __files.front();
        cv::Mat mat = cv::imread(path);
        __files.pop();
    }
    auto end = std::chrono::high_resolution_clock::now();

    // Calculate the duration
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    std::cout << "read image time: " << duration.count()/float(num_files)/1000.0 << " microseconds" << std::endl;
}

int Test::__generate_random(int start, int end) {
    // Use a random device as a source of randomness
    std::random_device rd;

    // Use the Mersenne Twister engine for random number generation
    std::mt19937 gen(rd());

    // Define the distribution for the specified range [m, n]
    std::uniform_int_distribution<int> dist(start, end);

    // Generate a random integer within the specified range
    return dist(gen);
}

Test::Test(const std::string &engine_file,
           const std::string &config_file,
           const std::string &task_name,
           const std::string &log_file,
           yolo::Type type,
           std::vector<std::string>& classes):
    __engine_file(engine_file),
    __config_file(config_file),
    __task_name(task_name),
    __log_file(log_file),
    __type(type), __classes(classes),
    __infer(new yolo::Infer(__engine_file, __config_file, __task_name, __log_file, __type)) {

    if(!__infer->success()) {
        std::cout << "init failed, check your config file, \
                      engine file or log config file" << std::endl;
        exit(-1);
    }
}

int Test::batch_img_path(const std::string &image_path,
                         const std::string &save_path, bool draw) {
    /* get_batch_size */
    int batch_size = __infer->get_batch_size();

    /* step3: read images */
    __get_files(image_path);
    int num_files = __files.size();
    std::cout << "num_files=" << num_files << std::endl;

    /* step4: inference */
    std::queue<std::string> infer_files;
    //auto start = std::chrono::high_resolution_clock::now();
    float total_time = 0.f;

    while(!__files.empty()) {

        for(int i=0; i<batch_size && !__files.empty(); ++i) {
            auto file = __files.front();
            printf("infer %s\n", file.c_str());
            infer_files.push(file);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        yolo::OutDetArray batched_result = __infer->batch_inference(infer_files);

        if(batched_result[0].bboxes.size()==1 && 
           batched_result[0].bboxes[0].class_label==-2){
            std::cout << "image size is bigger than max batch size" << std::endl;
            return -1;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());

        /* step5: draw results */
        std::cout << "draw results:" << save_path << std::endl;
        __infer->draw_results(batched_result, save_path);
    }

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "batch_img_path done" << std::endl;

    return 0;
}

//int Test::batch_mat(const std::queue<cv::Mat>& images,
//                    const std::string &save_path) {
int Test::batch_mat(std::queue<std::tuple<cv::Mat, slice::SliceType, std::string>>& infer_input,
                    const std::string &save_path, bool draw) {

    int batch_size = __infer->get_batch_size();

    //std::queue<std::tuple<cv::Mat, slice::SliceType, std::string>> batch_data,
    std::queue<cv::Mat> batch_in;

    int num_files = infer_input.size();
    float total_time = 0.f;

    while(!infer_input.empty()) {

        std::vector<cv::Mat> batch_imgs;
        std::vector<slice::SliceType> st;
        std::vector<std::string> img_name;

        std::queue<cv::Mat> batch_in;

        for(int i=0; i<batch_size && !infer_input.empty(); ++i) {
            std::tuple<cv::Mat, slice::SliceType, std::string> input = infer_input.front();

            cv::Mat imat = std::get<0>(input);
            batch_in.push(imat);

            batch_imgs.emplace_back(imat);

            st.emplace_back(std::get<1>(input));
            img_name.emplace_back(std::get<2>(input));

            infer_input.pop();
        }

        yolo::OutDetArray batched_result = __infer->batch_inference(batch_in);

        if(batched_result[0].bboxes.size()==1 && 
           batched_result[0].bboxes[0].class_label==-2){
            std::cout << "image size is bigger than max batch size" << std::endl;
            return -1;
        }

        for(int i=0; i<batch_size && !infer_input.empty(); ++i) {
            if(st[i] == slice::SliceType::Left)
                batched_result[i].image_path = save_path + "/T-L/" + img_name[i];
            else if(st[i] == slice::SliceType::Middle)
                batched_result[i].image_path = save_path + "/T-M/" + img_name[i];
            else if(st[i] == slice::SliceType::Right)
                batched_result[i].image_path = save_path + "/T-R/" + img_name[i];
        }

        if(draw) {
            std::cout << "draw results" << std::endl;
            draw_results(batched_result, batch_imgs);
        }
    }

    return 0;
}

void Test::draw_results(yolo::OutDetArray& result,
                        std::vector<cv::Mat>& batch_imgs) {
    
    if(result.size()==2) {
        printf("22222222222\n");
    }

    for (int ib = 0; ib < (int)result.size(); ++ib) {
        auto &objs = result[ib].bboxes;
        std::string image_path = result[ib].image_path;
        cv::Mat image = batch_imgs[ib];
        int i = 0;
        bool empty = false, has_defect = false;
        std::vector<std::string> debug_str;

        for (auto &obj : objs) {
            has_defect = true;

            // avoid emtpy image
            if(obj.class_label == -1) {
                empty = true;
                break;
            }

            uint8_t b, g, r;
            std::tie(b, g, r) = random_color(obj.class_label);
            cv::rectangle(image, cv::Point(obj.left, obj.top), 
                          cv::Point(obj.right, obj.bottom),
                          cv::Scalar(b, g, r), 5);

            auto name = __classes[obj.class_label];
            auto caption = cv::format("%s %.2f", name.c_str(), obj.confidence);
            int width = cv::getTextSize(caption, 0, 1, 2, nullptr).width + 10;
            cv::rectangle(image, cv::Point(obj.left - 3, obj.top - 33),
                          cv::Point(obj.left + width, obj.top), cv::Scalar(b, g, r), -1);
            cv::putText(image, caption, 
                        cv::Point(obj.left, obj.top - 5), 
                        0, 1, cv::Scalar::all(0), 
                        2, 16);
        }

        //std::string image_name = __get_image_name(image_path);
        if(!empty && has_defect && !image_path.empty()) {
            cv::imwrite(image_path, image);
        } else {
            std::cout << image_path << "no defects" << std::endl;
        }
    }
}

int Test::thread_img_path(const std::string &image_path,
                          const std::string &save_path) {

    /* step1: read images */
    __get_files(image_path);
    int num_files = __files.size();

    /* step2: start thread */
    std::cout << "start infer" << std::endl;
    if(!__infer->multi_thread_start()){
        std::cout << "multi thread start failed" << std::endl;
        return -1;
    }

    /* step3: inference */
    int imgs_left = num_files;
    int rand = 0;
    std::queue<std::string> sub_files;
    float total_time = 0.f;

    while(!__files.empty()) {

        //if(imgs_left < MAX_RANDOM) rand = imgs_left;
        //else rand = generate_random(1, MAX_RANDOM);
        if(imgs_left < MAX_RANDOM) rand = imgs_left;
        else rand = MAX_RANDOM;

        std::cout << "rand=" << rand << std::endl;
        for (int i = 0; i < rand; ++i) {
            auto path = __files.front();
            cv::Mat mat = cv::imread(path);
            sub_files.push(path);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        /* NOTE: inference here */
        yolo::OutDetArray result = __infer->multi_thread_infer(sub_files);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());
        imgs_left -= rand;

        /* step5: draw results */
        std::cout << "draw results" << std::endl;
        __infer->draw_results(result, save_path);
    }

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "thread_img_path done" << std::endl;

    return 0;
}

int Test::thread_mat(const std::string &image_path,
                     const std::string &save_path) {

    /* step1: read images */
    __get_files(image_path);
    int num_files = __files.size();

    /* step2: start thread */
    std::cout << "start infer" << std::endl;
    if(!__infer->multi_thread_start()){
        std::cout << "multi thread start failed" << std::endl;
        return -1;
    }

    /* step3: inference */
    int imgs_left = num_files;
    //yolo::OutArray all_result;
    int rand = 0;
    std::queue<cv::Mat> sub_images;
    std::vector<cv::Mat> sub_org_images;
    float total_time = 0.f;
    int cnt = 0;

    while(!__files.empty()) {

        if(imgs_left < MAX_RANDOM) rand = imgs_left;
        else rand = MAX_RANDOM;

        std::cout << "rand=" << rand << std::endl;
        for (int i = 0; i < rand; ++i) {
            auto path = __files.front();
            cv::Mat mat = cv::imread(path);
            sub_images.push(mat);
            sub_org_images.emplace_back(mat);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        /* NOTE: inference here */
        yolo::OutDetArray result = __infer->multi_thread_infer(sub_images);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());
        imgs_left -= rand;

        /* step5: draw results */
        std::cout << "draw results" << std::endl;
        __infer->draw_results(result, 
                              sub_org_images, 
                              save_path, cnt);
        cnt++;
        sub_org_images.clear();
    }

    //LOG4CPLUS_INFO(__logger, 
    //                "save result images to " << save_path);

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "thread_mat done" << std::endl;

    return 0;
}

/* classification */
int Test::batch_img_path_cls(const std::string &image_path,
                             const std::string &save_path) {
    /* get_batch_size */
    int batch_size = __infer->get_batch_size();

    /* step3: read images */
    __get_files(image_path);
    int num_files = __files.size();
    std::cout << "num_files=" << num_files << std::endl;

    /* step4: inference */
    std::queue<std::string> infer_files;
    //auto start = std::chrono::high_resolution_clock::now();
    float total_time = 0.f;

    while(!__files.empty()) {

        for(int i=0; i<batch_size && !__files.empty(); ++i) {
            auto file = __files.front();
            infer_files.push(file);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        yolo::OutClsArray batched_result = __infer->batch_inference_cls(infer_files);

        if(batched_result[0].image_path=="") {
            std::cout << "image size is bigger than max batch size" << std::endl;
            return -1;
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());
    }

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "batch_img_path done" << std::endl;

    return 0;
}

int Test::batch_mat_cls(const std::string &image_path,
                        const std::string &save_path) {

    /* step2: get_batch_size */
    int batch_size = __infer->get_batch_size();

    /* step3: read images */
    //std::queue<std::string> __files;
    std::queue<cv::Mat> images;
    __get_files(image_path);
    int num_files = __files.size();
    float total_time = 0.f;

    while(!__files.empty()) {

        for(int i=0; i<batch_size && !__files.empty(); ++i) {
            auto file = __files.front();
            cv::Mat image = cv::imread(file);
            images.push(image);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        yolo::OutDetArray batched_result = __infer->batch_inference(images);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());
    }

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "batch_mat done" << std::endl;

    return 0;
}

int Test::thread_img_path_cls(const std::string &image_path,
                              const std::string &save_path) {

    /* step1: read images */
    __get_files(image_path);
    int num_files = __files.size();

    /* step2: start thread */
    std::cout << "start infer" << std::endl;
    if(!__infer->multi_thread_start()){
        std::cout << "multi thread start failed" << std::endl;
        return -1;
    }

    /* step3: inference */
    int imgs_left = num_files;
    //yolo::OutArray all_result;
    int rand = 0;
    std::queue<std::string> sub_files;
    float total_time = 0.f;

    while(!__files.empty()) {

        //if(imgs_left < MAX_RANDOM) rand = imgs_left;
        //else rand = generate_random(1, MAX_RANDOM);
        if(imgs_left < MAX_RANDOM) rand = imgs_left;
        else rand = MAX_RANDOM;

        for (int i = 0; i < rand; ++i) {
            auto path = __files.front();
            sub_files.push(path);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        /* NOTE: inference here */
        yolo::OutClsArray result = __infer->multi_thread_infer_cls(sub_files);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());
        imgs_left -= rand;
    }

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "thread_img_path done" << std::endl;

    return 0;
}

int Test::thread_mat_cls(const std::string &image_path,
                         const std::string &save_path) {

    /* step1: read images */
    __get_files(image_path);
    int num_files = __files.size();

    /* step2: start thread */
    std::cout << "start infer" << std::endl;
    if(!__infer->multi_thread_start()){
        std::cout << "multi thread start failed" << std::endl;
        return -1;
    }

    /* step3: inference */
    int imgs_left = num_files;
    int rand = 0;
    std::queue<cv::Mat> sub_images;
    std::vector<cv::Mat> sub_org_images;
    float total_time = 0.f;

    while(!__files.empty()) {

        if(imgs_left < MAX_RANDOM) rand = imgs_left;
        else rand = MAX_RANDOM;

        std::cout << "rand=" << rand << std::endl;
        for (int i = 0; i < rand; ++i) {
            auto path = __files.front();
            cv::Mat mat = cv::imread(path);
            sub_images.push(mat);
            sub_org_images.emplace_back(mat);
            __files.pop();
        }

        auto start = std::chrono::high_resolution_clock::now();
        /* NOTE: inference here */
        yolo::OutDetArray result = __infer->multi_thread_infer(sub_images);
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        total_time += static_cast<float>(duration.count());
        imgs_left -= rand;

        /* step5: draw results */
        sub_org_images.clear();
    }

    std::cout << "infer one image time: " << total_time / float(num_files) / 1000.0 << " microseconds" << std::endl;
    std::cout << "thread_mat done" << std::endl;

    return 0;
}

