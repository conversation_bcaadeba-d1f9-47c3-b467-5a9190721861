#include "test_infer.hpp"
#include "test.hpp"

#ifdef WIN32
void DetectTest::__get_files(const std::string& path, bool left) {
    intptr_t handle;
    struct _finddata_t fileInfo;

    handle = _findfirst((path + "\\*").c_str(), &fileInfo);
    if (handle == -1) {
        std::cerr << "Error finding files in directory: " << 
                     path.c_str() << std::endl;
        return;
    }

    do {
        if (!(fileInfo.attrib & _A_SUBDIR)) {
            std::string img_p = path;
            if(left)
                __l_files.push(img_p.append("\\").append(fileInfo.name));
            else
                __r_files.push(img_p.append("\\").append(fileInfo.name));
        }
    } while (_findnext(handle, &fileInfo) == 0);

    _findclose(handle);
}

#else

void DetectTest::__get_files(const std::string &path, bool left) {
    DIR *dir;
    struct dirent *ptr;
    if ((dir = opendir(path.c_str())) == NULL)
    {
        perror("Open dir error...");
        return;
    }

    while ((ptr = readdir(dir)) != NULL)
    {
        // current dir or parent dir
        if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0) {
            continue;
        } else if (ptr->d_type == 8) {
            std::string strFile;
            strFile = path;
            strFile += "/";
            strFile += ptr->d_name;
            if(left) __l_files.push(strFile);
            else __r_files.push(strFile);
        }
        else
            continue;
    }
    closedir(dir);
    return;
}
#endif // system

//void DetectTest::thread_img_path(bool detect,
//                                yolo::Type type, 
//                                std::string engine_file, 
//                                std::string config_file, 
//                                std::string task_name, 
//                                std::string log_file,
//                                std::vector<std::string>& classes) {
//
//    /* create detection obj */
//    std::shared_ptr<Test> infer = std::make_shared<Test>(engine_file, 
//                config_file, task_name, log_file, type, classes);
//
//    std::string l_path = _image_path+"/L";
//    std::string r_path = _image_path+"/R";
//
//    /* step1: read images */
//    //__get_files(_image_path);
//    __get_files(l_path, true);
//    int l_num_files = __l_files.size();
//
//    //printf("l_path: %s, num=%lu\n", l_path.c_str(), __l_files.size());
//    //for(int i=0; i<l_num_files; ++i) {
//    //    std::string img_name = __l_files.front();
//    //    printf("i=%d, num=%lu, %s\n", i, __l_files.size(), img_name.c_str());
//    //    //__l_files.pop();
//    //}
//
//    __get_files(r_path, false);
//    int r_num_files = __r_files.size();
//
//    //printf("r_path: %s, num=%lu\n", r_path.c_str(), __r_files.size());
//    //for(int i=0; i<r_num_files; ++i) {
//    //    std::string img_name = __r_files.front();
//    //    printf("i=%d, num=%lu, %s\n", i, __r_files.size(), img_name.c_str());
//    //    //__r_files.pop();
//    //}
//
//    /* step2: start thread */
//    std::cout << "start process" << std::endl;
//    if(!__infer->multi_thread_start()){
//        std::cout << "multi thread start failed" << std::endl;
//        return;
//    }
//
//    /* step3: inference */
//    int l_imgs_left = l_num_files;
//    int r_imgs_left = r_num_files;
//    int rand = 0;
//    std::queue<std::pair<bool, std::string>> sub_files;
//    std::queue<std::tuple<cv::Mat, slice::SliceType, std::string>> infer_input;
//
//    auto start = std::chrono::high_resolution_clock::now();
//    while(!__l_files.empty()) {
//
//        if(l_imgs_left < MAX_RANDOM) rand = l_imgs_left;
//        else rand = MAX_RANDOM;
//
//        for (int i = 0; i < rand; ++i) {
//            auto path = __l_files.front();
//            sub_files.push(std::make_pair(true, path));
//            __l_files.pop();
//        }
//
//        /* NOTE: inference here */
//        auto ret = __infer->multi_thread_infer(sub_files);
//        l_imgs_left -= rand;
//
//        /* write imgs to disk */
//        for(auto& r: ret) {
//            cv::Mat mat = std::get<0>(r);
//            slice::SliceType st = std::get<1>(r);
//            std::string img_name = std::get<2>(r);
//
//            infer_input.push(std::make_tuple(mat, st, img_name));
//
//            //std::string img_path;
//            //auto start = std::chrono::high_resolution_clock::now();
//            //switch (st) {
//            //    case slice::SliceType::Left:
//            //        img_path = _save_path+"/T-L/"+img_name;
//            //        cv::imwrite(img_path, mat);
//            //        break;
//            //    case slice::SliceType::Middle:
//            //        img_path = _save_path+"/T-M/"+img_name;
//            //        cv::imwrite(img_path, mat);
//            //        break;
//            //    case slice::SliceType::Right:
//            //        img_path = _save_path+"/T-R/"+img_name;
//            //        cv::imwrite(img_path, mat);
//            //        break;
//            //    default:
//            //        printf("SliceType err");
//            //        break;
//            //}
//            //auto end = std::chrono::high_resolution_clock::now();
//            //auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
//            //float t = static_cast<float>(duration.count());
//            //printf("ssssssave %f ms per img\n", t/1000);
//        }
//    }
//
//    while(!__r_files.empty()) {
//
//        if(r_imgs_left < MAX_RANDOM) rand = r_imgs_left;
//        else rand = MAX_RANDOM;
//
//        for (int i = 0; i < rand; ++i) {
//            auto path = __r_files.front();
//            sub_files.push(std::make_pair(false, path));
//            __r_files.pop();
//        }
//
//        /* NOTE: inference here */
//        auto ret = __infer->multi_thread_infer(sub_files);
//        r_imgs_left -= rand;
//
//        /* write imgs to disk */
//        for(auto& r: ret) {
//            cv::Mat mat = std::get<0>(r);
//            slice::SliceType st = std::get<1>(r);
//            std::string img_name = std::get<2>(r);
//            infer_input.push(std::make_tuple(mat, st, img_name));
//
//            //std::string img_path;
//            //auto start = std::chrono::high_resolution_clock::now();
//            //switch (st) {
//            //    case slice::SliceType::Left:
//            //        img_path = _save_path+"/T-L/"+img_name;
//            //        cv::imwrite(img_path, mat);
//            //        break;
//            //    case slice::SliceType::Middle:
//            //        img_path = _save_path+"/T-M/"+img_name;
//            //        cv::imwrite(img_path, mat);
//            //        break;
//            //    case slice::SliceType::Right:
//            //        img_path = _save_path+"/T-R/"+img_name;
//            //        cv::imwrite(img_path, mat);
//            //        break;
//            //    default:
//            //        printf("SliceType err");
//            //        break;
//            //}
//            //auto end = std::chrono::high_resolution_clock::now();
//            //auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
//            //float t = static_cast<float>(duration.count());
//            //printf("sssssssave %f ms per img\n", t/1000);
//        }
//    }
//
//    if(detect) {
//        //std::string split_path = _save_path+"/T-L";
//        //std::string save_path = _save_path+"/split_detect/T-L";
//        //int l_infer_ret = infer->batch_img_path(split_path, save_path);
//
//        //split_path = _save_path+"/T-M";
//        //save_path = _save_path+"/split_detect/T-M";
//        //int m_infer_ret = infer->batch_img_path(split_path, save_path);
//
//        //split_path = _save_path+"/T-R";
//        //save_path = _save_path+"/split_detect/T-R";
//        //int r_infer_ret = infer->batch_img_path(split_path, save_path);
//
//        std::string save_path = _save_path+"/split_detect/";
//        int ret = infer->batch_mat(infer_input, save_path, true);
//
//        auto end = std::chrono::high_resolution_clock::now();
//        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
//        float t = static_cast<float>(duration.count());
//        printf("%f ms per img\n", t/(l_num_files+r_num_files)/1000);
//    }
//
//    if(!detect) {
//        auto end = std::chrono::high_resolution_clock::now();
//        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
//        float t = static_cast<float>(duration.count());
//        printf("%f ms per img\n", t/(l_num_files+r_num_files)/1000);
//    }
//}

void DetectTest::single_img_slice(bool detect,
                                yolo::Type type, 
                        std::string engine_file, 
                        std::string config_file, 
                        std::string task_name, 
                        std::string log_file,
                        std::vector<std::string>& classes) {

    std::shared_ptr<Test> infer = std::make_shared<Test>(engine_file, 
                config_file, task_name, log_file, type, classes);

    std::string l_path = _image_path+"/L";
    std::string r_path = _image_path+"/R";

    /* step1: read images */
    __get_files(l_path, true);
    int l_num_files = __l_files.size();
    __get_files(r_path, false);
    int r_num_files = __r_files.size();

    /* step3: inference */
    std::queue<std::tuple<cv::Mat, slice::SliceType, std::string>> infer_input;

    auto start = std::chrono::high_resolution_clock::now();
    while(!__l_files.empty()) {

        auto path = __l_files.front();
        cv::Mat mat = cv::imread(path, cv::IMREAD_GRAYSCALE);
        size_t position = path.find_last_of("/\\");
        std::string img_name = path.substr(position+1, path.length()-position-1);

        std::tuple<bool, cv::Mat, std::string> data = std::make_tuple(true, mat, img_name);
        __l_files.pop();

        /* NOTE: inference here */
        auto ret = __infer->single_worker(data);

        /* write imgs to disk */
        for(auto& r: ret) {
            cv::Mat mat = std::get<0>(r);
            slice::SliceType st = std::get<1>(r);
            std::string img_name = std::get<2>(r);
            infer_input.push(std::make_tuple(mat, st, img_name));
        }
    }

    while(!__r_files.empty()) {

        auto path = __r_files.front();
        cv::Mat mat = cv::imread(path, cv::IMREAD_GRAYSCALE);

        size_t position = path.find_last_of("/\\");
        std::string img_name = path.substr(position+1, path.length()-position-1);

        std::tuple<bool, cv::Mat, std::string> data = std::make_tuple(false, mat, img_name);
        __r_files.pop();

        /* NOTE: inference here */
        auto ret = __infer->single_worker(data);

        std::string img_path;
        /* write imgs to disk */
        for(auto& r: ret) {
            cv::Mat mat = std::get<0>(r);
            slice::SliceType st = std::get<1>(r);
            std::string img_name = std::get<2>(r);
            infer_input.push(std::make_tuple(mat, st, img_name));
        }
    }

    if(detect) {
        std::string save_path = _save_path+"/split_detect/";
        int ret = infer->batch_mat(infer_input, save_path, true);

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        float t = static_cast<float>(duration.count());
        printf("%f ms per img\n", t/(l_num_files+r_num_files)/1000);
    }

    if(!detect) {
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        float t = static_cast<float>(duration.count());
        printf("%f ms per img\n", t/(l_num_files+r_num_files)/1000);
    }
}
