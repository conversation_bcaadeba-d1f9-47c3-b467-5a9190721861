#include "test.hpp"


#ifdef WIN32
const std::string engine_file("D:\\yolov8\\yolo_infer\\workspace\\best.transd.engine");
const std::string config_file("D:\\yolov8\\yolo_infer\\yolo_infer.cfg");
const std::string image_path("D:\\yolov8\\yolo_infer\\tests\\test_imgs");
const std::vector<std::string> save_path = {
    "D:\\yolov8\\yolo_infer\\tests\\batch_img_path_result",
    "D:\\yolov8\\yolo_infer\\tests\\thread_img_path_result",
    "D:\\yolov8\\yolo_infer\\tests\\batch_mat_result",
    "D:\\yolov8\\yolo_infer\\tests\\thread_mat_result"
};
#else

// shanghai kj
const std::string kj_engine_file("/home/<USER>/workspace/data/上海专项数据/训练数据/扣件/model_2/shanghai_kj.transd.engine");
const std::string kj_config_file("/home/<USER>/workspace/data/上海专项数据/训练数据/扣件/model_2/shanghai_kj.cfg");

// shanghai yiwu 
const std::string yiwu_engine_file("/home/<USER>/workspace/data/上海专项数据/训练数据/异物/model_7/shanghai_yiwu.transd.engine");
const std::string yiwu_config_file("/home/<USER>/workspace/data/上海专项数据/训练数据/异物/model_7/shanghai_yiwu.cfg");
std::vector<std::string> yiwu_classes = {"YWQT", "YWCJ", "YWSS", "YWXZ"};

const std::vector<std::string> shanghai_save_path = {
    "/home/<USER>/workspace/data/raymond/shanghai_test_results/kj",
    "/home/<USER>/workspace/tmp/shanghai_yiwu/results"
};
#endif


int main () {
    //const std::string image_path = "/home/<USER>/workspace/tmp/splits/L"; 
    //const std::string save_path = "/home/<USER>/workspace/tmp/splits/results/T-L"; 
    const std::string image_path = "/home/<USER>/workspace/tmp/splits"; 
    const std::string save_path = "/home/<USER>/workspace/tmp/splits/results"; 

    int mem_align = 32;
    int max_block_size = 256;
    int max_batch_size = 8;
    int max_queue_imgs = 160;

    float left_alpha = 1.0+120/100; 
    float left_beta = 15;

    float right_alpha = 1.0+100/100; 
    float right_beta = 10;

    int img_width = 3496, img_height = 2000;

    /* [min, max) ==> width = max-min */
    int left_min = 70;
    int left_max = left_min+2060;
    int middle_min = 2059;
    int middle_max = middle_min+1437;
    int right_min = 1436;
    int right_max = right_min+2060;

    DetectTest prt(image_path, save_path,
                  left_alpha, left_beta,
                  right_alpha, right_beta,
                  img_width, img_height,
                  left_min, left_max,
                  middle_min, middle_max,
                  right_min, right_max,
                  mem_align,
                  max_block_size,
                  max_batch_size,
                  max_queue_imgs);

    /* multi-thread infer, read image in lib */
    //prt.thread_img_path();
    //prt.single_img_slice();

    /* detect */
    //bool detect = false;
    bool detect = true;
    //prt.thread_img_path(detect, yolo::Type::V8Det,
    //                yiwu_engine_file, yiwu_config_file, 
    //                "yiwu", "yiwu_log", yiwu_classes);

    //prt.single_img_slice(detect, yolo::Type::V8Det,
    //                yiwu_engine_file, yiwu_config_file, 
    //                "yiwu", "yiwu_log", yiwu_classes);

    return 0;
}
