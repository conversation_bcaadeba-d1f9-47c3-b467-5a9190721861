#ifndef __MEMORY_H__
#define __MEMORY_H__

#include "common.h"

void malloc_error(const size_t size, 
                  const char * const filename, 
                  const char * const funcname, 
                  const int line);

void calloc_error(const size_t size, 
                  const char * const filename, 
                  const char * const funcname, 
                  const int line);

void realloc_error(const size_t size, 
                   const char * const filename, 
                   const char * const funcname, 
                   const int line);

void *xmalloc_location(const size_t size, 
                       const char * const filename, 
                       const char * const funcname, 
                       const int line);

void *xrealloc_location(void *ptr, 
                        const size_t size, 
                        const char * const filename, 
                        const char * const funcname, 
                        const int line);

void *xcalloc_location(const size_t nmemb, 
                       const size_t size, 
                       const char * const filename, 
                       const char * const funcname, 
                       const int line);

#endif
