#ifndef __PARSER_H__
#define __PARSER_H__

#include "list.h"
#include "common.h"
#include "session.h"

#ifdef __cplusplus
extern "C" {
#endif

char *option_find(list *l, char *key);
char *option_find_str(list *l, char *key, char *def);
int option_find_int(list *l, char *key, int def);
void parse_classes(char *src, Task_s *task_session);
//void parse_infer_options(list *options);

void parse_task_options(list *options, Task_s *task_session);
void parse_preprocess_options(list *options, Pre_s *pre_session);
void parse_postprocess_options(list *options, Post_s *post_session);
void parse_gpu_options(list *options, GPU_s *gpu_session);
void parse_cpu_options(list *options, CpuMt_s *cpu_session);

#ifdef __cplusplus
}
#endif

#endif

