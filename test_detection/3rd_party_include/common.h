#ifndef __COMMON_H__
#define __COMMON_H__

#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <limits.h>
#include <uchar.h>

#define DARKNET_LOC __FILE__, __func__, __LINE__
#define xmalloc(s)      xmalloc_location(s, DARKNET_LOC)
#define xcalloc(m, s)   xcalloc_location(m, s, DARKNET_LOC)
#define xrealloc(p, s)  xrealloc_location(p, s, DARKNET_LOC)

#ifdef __cplusplus
extern "C" {
#endif

void error(const char * const msg, 
           const char * const filename, 
           const char * const funcname, 
           const int line);

const char* size_to_IEC_string(const size_t size);

#ifdef __cplusplus
}
#endif

#endif

