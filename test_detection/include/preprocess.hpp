#ifndef __PRE_HPP
#define __PRE_HPP

#include <opencv2/opencv.hpp>
#include "memory.hpp"

namespace yolo {

struct Image {
    std::string image_path;
    size_t width = 0, height = 0, channel = 1;
    const void *bgrptr = nullptr;

    Image() = default;
    Image(const void *bgrptr, 
          int width, int height, 
          const std::string image_path) : 
        bgrptr(bgrptr), width(width), height(height),
        image_path(image_path) {}
};

class Preprocess {
public:
    Preprocess() = default;
    Preprocess(size_t mem_align,
               int max_block_size,
               bool visualize=false):
               _visualize_affine(visualize),
               __max_block_size(max_block_size), 
               __mem_align(mem_align) {}

    inline void set_dst_dim(std::vector<size_t> network_input_shape) {
        _dst_dims = network_input_shape;
    }

    void preprocess(int ibatch,
                    const Image &image,
                    std::shared_ptr<Memory<uint8_t>> preprocess_buffer,
                    float *input_device,
                    bool is_cls,
                    cudaStream_t &stream);

protected:
    /* calc affine matrix  and its reverse */
    void _compute_affine_matrix(const std::vector<size_t> &src,
                                const std::vector<size_t> &dst);

    void _warp_affine_bilinear_and_normalize_plane(uint8_t *src, 
                                                  int src_line_size, 
                                                  int src_width,
                                                  int src_height,
                                                  int dst_width, 
                                                  int dst_height, 
                                                  float *dst, 
                                                  float *matrix_2_3,
                                                  uint8_t padding_value, 
                                                  bool is_cls,
                                                  cudaStream_t &stream);
private:
    inline int __upbound(const size_t n) {
        return (n + __mem_align - 1) / __mem_align * __mem_align; 
    }

public:
    bool _visualize_affine;
    cv::Mat _output;

protected:
    float _M[9];    // Affine Matrix
    float _M_i[9];  // inverse of Affine Matrix

    // [width, height, channel]
    std::vector<size_t> _dst_dims;

private:
    const size_t __mem_align;
    const int __max_block_size;
};

} // namespace yolo
#endif
