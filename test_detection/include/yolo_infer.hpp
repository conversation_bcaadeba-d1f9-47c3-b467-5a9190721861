#ifndef __YOLOINFER_HPP__
#define __YOLOINFER_HPP__

#include "infer_api.hpp"

namespace yolo {

struct ImageIn {
    ImageIn(const cv::Mat &image, const std::string &name):
        image(image), image_path(name) {}

    cv::Mat image;
    const std::string image_path;
};

Image cvimg(const ImageIn &in);

class Infer: public InferApi {
public:
    Infer(const std::string &engine_file,
          const std::string &config_file,
          const std::string &task_name,
          const std::string &log_file,
          yolo::Type type): 
        __engine_file(engine_file), 
        __config_file(config_file),
        __task_name(task_name),
        __log_file(log_file),
        __type(type), 
        _success(true) {

        /* init log */
        //__logger = init_spdlog("logger.txt");
        __logger = init_spdlog(__log_file, __task_name);
        __init(engine_file, config_file, type);
    }

    inline bool success() {
        return _success;
    }

    inline int get_batch_size() {
        return __max_batch_size;
    }

    /* detection */
    virtual OutDetArray batch_inference(std::queue<std::string> &files) override;
    virtual OutDetArray batch_inference(std::queue<cv::Mat> &images) override;
    /* classification */
    virtual OutClsArray batch_inference_cls(std::queue<std::string> &files) override;
    virtual OutClsArray batch_inference_cls(std::queue<cv::Mat> &images) override;

    bool multi_thread_start();
    /* detection */
    virtual OutDetArray multi_thread_infer(std::queue<std::string> &files) override;
    virtual OutDetArray multi_thread_infer(std::queue<cv::Mat> &images) override;

    /* classification */
    virtual OutClsArray multi_thread_infer_cls(std::queue<std::string> &files) override;
    virtual OutClsArray multi_thread_infer_cls(std::queue<cv::Mat> &images) override;

    virtual void draw_results(OutDetArray &result,
                      const std::string &save_path) override;

    virtual void draw_results(OutDetArray &all_result,
                      std::vector<cv::Mat> &images,
                      const std::string &save_path,
                      int cnt) override;

    virtual void draw_results(OutDetArray &batched_result,
                      const std::string &save_path, 
                      int cnt);


private:
    void __init(const std::string &engine_file,
                const std::string &config_file,
                yolo::Type type);

    void __device_info(int* device_count);

    std::string __get_image_name(const std::string &path);
    void __log_conf(const std::string &config_file,
                    yolo::ParseConf &conf);

private:
    const std::string __engine_file;
    const std::string __config_file;
    const std::string __log_file;
    const std::string __task_name;
    const std::string __log_config_file;

    std::vector<std::string> __classes;
    int __max_queue_imgs;
    bool __timer;
    bool __debug_log, __err_log;
    int __micro_sec_laytency;

    std::shared_ptr<yolo::YoloInfer> __yoloinfer;
    yolo::Type __type;
    int __max_batch_size;
    std::vector<ImageIn> __images;

    trt::Timer timer;
    cudaStream_t __stream;
    cpm::Instance<yolo::OutPut, cpm::CpmImage, yolo::Image, yolo::YoloInfer> _cpm_infer;

    bool _success;

    std::shared_ptr<spdlog::async_logger> __logger;
};

} // namespace yolo

#endif
