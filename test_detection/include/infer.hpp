#ifndef __INFER_HPP__
#define __INFER_HPP__

#include "log.hpp"
#include "common.hpp"

namespace trt {
using namespace nvinfer1;

template <typename TrT_T>
inline void __destroy_nvidia_pointer(TrT_T *ptr) {
  if (ptr) ptr->destroy();
}

class NvLogger: public ILogger {
  virtual void log(Severity severity, const char *msg) noexcept override {
    if (severity == Severity::kINTERNAL_ERROR) {
      _INFO("NVInfer INTERNAL_ERROR: %s", msg);
      abort();
    } else if (severity == Severity::kERROR) {
      _INFO("NVInfer: %s", msg);
    }
  }
};

class NvContext {
public:
    NvContext(): context_(nullptr), engine_(nullptr), runtime_(nullptr) {}
    virtual ~NvContext() { __destroy(); }
    bool construct(const void *pdata, size_t size);

private:
    inline void __destroy() {
        context_.reset();
        engine_.reset();
        runtime_.reset();
    }

public:
    std::shared_ptr<IExecutionContext> context_;
    std::shared_ptr<ICudaEngine> engine_;
    std::shared_ptr<IRuntime> runtime_;
};

/* virtual base */
class TrTInfer {
public:
    virtual bool forward(const std::vector<void *> &bindings, 
                         void *stream = nullptr,
                         void *input_consum_event = nullptr) = 0;

    virtual int index(const std::string &name) = 0;

    virtual std::vector<int> dynamic_dims(const std::string &name) = 0;
    virtual std::vector<int> dynamic_dims(int ibinding) = 0;
    virtual std::vector<int> static_dims(const std::string &name) = 0;
    virtual std::vector<int> static_dims(int ibinding) = 0;

    virtual int numel(const std::string &name) = 0;
    virtual int numel(int ibinding) = 0;

    virtual int num_bindings() = 0;
    virtual bool is_input(int ibinding) = 0;

    virtual bool set_dynamic_dims(const std::string &name, const std::vector<int> &dims) = 0;
    virtual bool set_dynamic_dims(int ibinding, const std::vector<int> &dims) = 0;
    virtual DType dtype(const std::string &name) = 0;
    virtual DType dtype(int ibinding) = 0;

    virtual bool has_dynamic_dim() = 0;
    virtual void print() = 0;
};

class TrTImpl: public TrTInfer {
public:
    //TrTImpl();
    virtual ~TrTImpl() = default;

    void setup();
    bool load(const std::string &file);
    bool construct(const void *pdata, size_t size);

    /* override functions */
    virtual bool forward(const std::vector<void *> &bindings, 
                         void *stream = nullptr,
                         void *input_consum_event = nullptr) override;

    virtual int index(const std::string &name) override;

    virtual std::vector<int> dynamic_dims(const std::string &name) override;
    virtual std::vector<int> dynamic_dims(int ibinding) override;
    virtual std::vector<int> static_dims(const std::string &name) override;
    virtual std::vector<int> static_dims(int ibinding) override;

    virtual int numel(const std::string &name) override;
    virtual int numel(int ibinding) override;

    virtual int num_bindings() override;
    virtual bool is_input(int ibinding) override;

    virtual bool set_dynamic_dims(const std::string &name, 
                              const std::vector<int> &dims) override;
    virtual bool set_dynamic_dims(int ibinding, 
                              const std::vector<int> &dims) override;
    virtual DType dtype(const std::string &name) override;
    virtual DType dtype(int ibinding) override;

    virtual bool has_dynamic_dim() override;
    virtual void print() override;

public:
    std::shared_ptr<NvContext> nv_context;
    std::unordered_map<std::string, int> binding_name_to_index_;

private:
    std::string format_shape(const Dims &shape);
    //log4cplus::Logger __logger;
};

std::shared_ptr<TrTInfer> load(const std::string &file);
//std::shared_ptr<TrTImpl> load(const std::string &file);

}  // namespace trt

#endif  // __INFER_HPP__

