#ifndef __PARSE_CONF__
#define __PARSE_CONF__

#include "read_cfg.h"
#include "common.hpp"

namespace yolo {

/* singleton class */
class ParseConf {
public:
    //static ParseConf& getInstance(const std::string &config_file) {
    //    static ParseConf instance(config_file);
    //    return instance;
    //}

    ParseConf(const std::string &config_file):
        __config_file(config_file),
        task_session(Task_s()), 
        pre_session(Pre_s()), 
        post_session(Post_s()), 
        gpu_session(GPU_s()), 
        cpu_session(CpuMt_s()),
        success(true) {

        __parse_config(config_file);
    }

    /* copies are not allowed */
    ParseConf(const ParseConf&) = delete;
    ParseConf& operator=(const ParseConf&) = delete;

    void __parse_config(const std::string &config_file);

private:
    std::string __config_file;

public:
    Task_s task_session;
    Pre_s pre_session;
    Post_s post_session;
    GPU_s gpu_session;
    CpuMt_s cpu_session;
    bool success;
};


}// namespace yolo

#endif

