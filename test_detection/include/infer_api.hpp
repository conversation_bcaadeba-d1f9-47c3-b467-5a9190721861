#ifndef __INFERAPI_HPP__
#define __INFERAPI_HPP__

#include "yolo.hpp"
#include "util.hpp"
#include "consumer_producer.hpp"

namespace yolo {

struct OutDet{
    explicit OutDet(BoxArray &bboxes, std::string path):
        bboxes(bboxes), image_path(path) {}

    BoxArray bboxes;
    std::string image_path;
};

struct OutCls{
    explicit OutCls(ClassificationMap &clsr, std::string path):
        cls(clsr), image_path(path) {}

    explicit OutCls(ClassificationMap &&clsr, std::string path):
        cls(clsr), image_path(path) {}

    ClassificationMap cls;  // valid only in classification task
    std::string image_path;
};


typedef std::vector<OutDet> OutDetArray;
typedef std::vector<OutCls> OutClsArray;

class InferApi {
public:
    virtual ~InferApi() {}

    /* serial inference */
    /**
     * @brief: get batch size of engine model
     *
     * @return batch_size
     *     get value recored in yolo_infer.cfg max_batch_size
     *
     */
    virtual inline int get_batch_size()=0;

    /**
     * @brief: whether initialize success
     *
     * @return success
     *     if turn true, initialization success,
     *     otherwise return false
     *
     */
    virtual inline bool success()=0;

    /**
     * @brief: one batch images inference
     *
     * @param files
     *     paths of images need to be infered
     *     size of queue MUST smaller or equal to batch size in engie model 
     *     which is recoreded in yolo_infer.cfg max_batch_size and 
     *     you can get this value by calling get_batch_size
     *
     * @return std::vector<BoxArray> 
     *     results of inference bbox coordinate, confidence and label
     *     BoxArray defined in yolo.hpp
     * 
     */
    virtual OutDetArray batch_inference(std::queue<std::string> &files)=0;
    virtual OutDetArray batch_inference(std::queue<cv::Mat> &images)=0;

    /* classification */
    virtual OutClsArray batch_inference_cls(std::queue<std::string> &files)=0;
    virtual OutClsArray batch_inference_cls(std::queue<cv::Mat> &images)=0;

    /* concurrent inference */
    /**
     * @brief: start worker thread which completes detection
     *
     * @return void
     */
    virtual bool multi_thread_start()=0;

    /**
     * @brief: infer input files
     *
     * @param files
     *     arbitrary number of files, you can put all infer files in queue
     *
     * @return OutDetArray
     *     results of inference bbox coordinate, confidence, label and image path
     * 
     */
    virtual OutDetArray multi_thread_infer(std::queue<std::string> &files)=0;
    virtual OutDetArray multi_thread_infer(std::queue<cv::Mat> &images)=0;

    /* classification */
    virtual OutClsArray multi_thread_infer_cls(std::queue<std::string> &files)=0;
    virtual OutClsArray multi_thread_infer_cls(std::queue<cv::Mat> &images)=0;

    /**
     * @brief: draw bounding boxes in original images
     *
     * @param all_result
     *     results of inference bbox coordinate, confidence, label and image path
     *
     * @return void
     * 
     */
    virtual void draw_results(OutDetArray &result,
                              const std::string &save_path)=0;

    virtual void draw_results(OutDetArray &all_result,
                      std::vector<cv::Mat> &images,
                      const std::string &save_path,
                      int cnt)=0;
    /**
     * @brief: draw bounding boxes in original images
     *
     * @param batched_result
     *     results of inference bbox coordinate, confidence and label
     *
     * @return void
     * 
     */
    virtual void draw_results(OutDetArray &batched_result,
                      const std::string &save_path, 
                      int cnt)=0;
};

} // namespace yolo
#endif

