#ifndef __COMMON_HPP__
#define __COMMON_HPP__

#ifdef WIN32
#include <io.h>
#elif linux
#include <dirent.h>
#include <unistd.h>
#endif

#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <stdio.h>
#include <filesystem>

#include <NvInfer.h>
#include <cuda_runtime.h>
#include <stdarg.h>

#include <stdexcept>
#include <fstream>
#include <numeric>
#include <sstream>
#include <unordered_map>
#include <algorithm>
#include <chrono>

#include <initializer_list>
#include <memory>
#include <string>
#include <vector>
#include <thread>

#include <opencv2/opencv.hpp>

/* log */
//#include "spdlog.hpp"

namespace yolo {

/* 
   NUM_BOX_ELEMENT:
   left, top, right, bottom, confidence, label, 
   keepflag, row_index 
 */

enum class NormType : int { None = 0, MeanStd = 1, AlphaBeta = 2 };
enum class ChannelType : int { None = 0, SwapRB = 1 };

enum class Type: int {
    V8Det = 0,
    V8Seg = 1, 
    EffCls = 2
};
} // namespace yolo

namespace trt {

enum class DType: int{
    FLOAT=0, HALF=1, 
    INT8=2, INT32=3,
    BOOL=4, UINT8=5
};

} // namespace trt

std::vector<uint8_t> load_file(const std::string &file);
std::string file_name(const std::string &path, 
                      bool include_suffix);

#endif

