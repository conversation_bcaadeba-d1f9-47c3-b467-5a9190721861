#ifndef __SPDLOG_HPP__
#define __SPDLOG_HPP__

#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_TRACE

#include "spdlog/spdlog.h"
#include "spdlog/async.h"
#include "spdlog/sinks/daily_file_sink.h"
#include "spdlog/stopwatch.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/callback_sink.h"

#include <iostream>
#include <string>

//void init_spdlog(std::string log_file);
std::shared_ptr<spdlog::async_logger> init_spdlog(std::string log_file,
                                                  std::string task_name);

//#define INITLOG(LOG_FILE)     init_spdlog(LOG_FILE)

#define TRACE(...)     SPDLOG_TRACE(__VA_ARGS__)
#define DEBUG(...)     SPDLOG_DEBUG(__VA_ARGS__)
#define INFO(...)      SPDLOG_INFO(__VA_ARGS__)
#define WARN(...)      SPDLOG_WARN(__VA_ARGS__)
#define ERROR(...)     SPDLOG_ERROR(__VA_ARGS__)
#define CRITICAL(...)  SPDLOG_CRITICAL(__VA_ARGS__)

//单个日志文件
#define GETLOG(LOG_NAME) get_async_file_logger(LOG_NAME)

#define LOGGER_TRACE(__logger,...)     SPDLOG_LOGGER_TRACE(__logger,__VA_ARGS__)
#define LOGGER_DEBUG(__logger,...)     SPDLOG_LOGGER_DEBUG(__logger,__VA_ARGS__)
#define LOGGER_INFO(__logger,...)      SPDLOG_LOGGER_INFO(__logger,__VA_ARGS__)
#define LOGGER_WARN(__logger,...)      SPDLOG_LOGGER_WARN(__logger,__VA_ARGS__)
#define LOGGER_ERROR(__logger,...)     SPDLOG_LOGGER_ERROR(__logger,__VA_ARGS__)
#define LOGGER_CRITICAL(__logger,...)  SPDLOG_LOGGER_CRITICAL(__logger,__VA_ARGS__)

//时间统计宏
#define LOGSW() spdlog::stopwatch()

#endif

