#ifndef __READ_FILE_HPP__
#define __READ_FILE_HPP__

#include "common.hpp"

#ifdef WIN32
/*
void getFiles(std::string path, std::queue<std::string>& files) {
    //文件句柄
    long   hFile = 0;

    //文件信息，声明一个存储文件信息的结构体  
    struct _finddata_t fileinfo;
    std::string p;//字符串，存放路径

    if ((hFile = _findfirst(p.assign(path).append("\\*").c_str(), &fileinfo)) != -1) {
        do {
            //如果是目录,迭代之（即文件夹内还有文件夹）  
            if ((fileinfo.attrib &  _A_SUBDIR))
            {
                //文件名不等于"."&&文件名不等于".."
                //.表示当前目录
                //..表示当前目录的父目录
                //判断时，两者都要忽略，不然就无限递归跳不出去了！
                if (strcmp(fileinfo.name, ".") != 0 && strcmp(fileinfo.name, "..") != 0)
                    getFiles(p.assign(path).append("\\").append(fileinfo.name), files);
            }
            //如果不是,加入列表  
            else
                files.push(p.assign(path).append("\\").append(fileinfo.name));
		} while (_findnext(hFile, &fileinfo) == 0);
        //_findclose函数结束查找
        _findclose(hFile);
    }
}
*/
void getFiles(const std::string& path, std::queue<std::string>& files) {
	intptr_t handle;
	struct _finddata_t fileInfo;

	handle = _findfirst((path + "\\*").c_str(), &fileInfo);
	if (handle == -1) {
		std::cerr << "Error finding files in directory: " << path.c_str() << std::endl;
		return;
	}

	do {
		if (!(fileInfo.attrib & _A_SUBDIR)) {
			std::string img_p = path;
			files.push(img_p.append("\\").append(fileInfo.name));
		}
	} while (_findnext(handle, &fileInfo) == 0);

	_findclose(handle);
}

#else

void getFiles(const std::string &path, 
              std::queue<std::string>& files) {
    DIR *dir;
    struct dirent *ptr;
    if ((dir = opendir(path.c_str())) == NULL)
    {
        perror("Open dir error...");
        return;
    }

    while ((ptr = readdir(dir)) != NULL)
    {
        // current dir or parent dir
        if (strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0) {
            continue;
        } else if (ptr->d_type == 8) {
            std::string strFile;
            strFile = path;
            strFile += "/";
            strFile += ptr->d_name;
            files.push(strFile);
        }
        else
            continue;
    }
    closedir(dir);
    return;
}

#endif // system
#endif // header

