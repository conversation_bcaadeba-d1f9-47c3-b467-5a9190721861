#ifndef __TEST_INFER_HPP__
#define __TEST_INFER_HPP__

#include "slice_common.hpp"
#include "yolo_infer.hpp"
#include "timer.hpp"

#include <random>
#include <chrono>

#define MAX_RANDOM 100

class Test {
public:
    //Test() = default;
    ~Test() = default;
    Test(const std::string &engine_file, 
         const std::string &config_file,
         const std::string &task_name,
         const std::string &log_file,
         yolo::Type type,
         std::vector<std::string>& classes);

    /* detection */
    /* batch infer, read image in lib */
    int batch_img_path(const std::string &image_path,
                       const std::string &save_path, bool draw=true);

    /* multi-thread infer, read image in lib */
    int thread_img_path(const std::string &image_path,
                        const std::string &save_path);

    /* multi-thread infer, read image out lib */
    //int batch_mat(const std::string &image_path,
    //              const std::string &save_path);
    int batch_mat(std::queue<std::tuple<cv::Mat, slice::SliceType, std::string>>& infer_input,
                  const std::string &save_path, bool draw=true);

    /* multi-thread infer, read image out lib */
    int thread_mat(const std::string &image_path,
                   const std::string &save_path);

    /* classification */
    /* batch infer, read image in lib */
    int batch_img_path_cls(const std::string &image_path,
                           const std::string &save_path);

    int batch_mat_cls(const std::string &image_path,
                      const std::string &save_path);

    int thread_img_path_cls(const std::string &image_path,
                            const std::string &save_path);

    /* multi-thread infer, read image in lib */
    int thread_mat_cls(const std::string &image_path,
                       const std::string &save_path);

    void draw_results(yolo::OutDetArray& result,
                      std::vector<cv::Mat>& batch_imgs);
    
    std::tuple<uint8_t, uint8_t, uint8_t> random_color(int id) {
      float h_plane = ((((unsigned int)id << 2) ^ 0x937151) % 100) / 100.0f;
      float s_plane = ((((unsigned int)id << 3) ^ 0x315793) % 100) / 100.0f;
      return hsv2bgr(h_plane, s_plane, 1);
    }

    std::tuple<uint8_t, uint8_t, uint8_t> hsv2bgr(float h, float s, float v) {
      const int h_i = static_cast<int>(h * 6);
      const float f = h * 6 - h_i;
      const float p = v * (1 - s);
      const float q = v * (1 - f * s);
      const float t = v * (1 - (1 - f) * s);
      float r, g, b;
      switch (h_i) {
        case 0:
          r = v, g = t, b = p;
          break;
        case 1:
          r = q, g = v, b = p;
          break;
        case 2:
          r = p, g = v, b = t;
          break;
        case 3:
          r = p, g = q, b = v;
          break;
        case 4:
          r = t, g = p, b = v;
          break;
        case 5:
          r = v, g = p, b = q;
          break;
        default:
          r = 1, g = 1, b = 1;
          break;
      }
      return std::make_tuple(static_cast<uint8_t>(b * 255), 
                             static_cast<uint8_t>(g * 255),
                             static_cast<uint8_t>(r * 255));
    }



    /* segmentation */
    /*
    int thread_img_path_segmentation(const std::string &image_path,
                                     const std::string &save_path);

    int thread_mat_segmentation(const std::string &image_path,
                                const std::string &save_path);

    int batch_img_path_segmentation(const std::string &image_path,
                                    const std::string &save_path);

    int batch_mat_segmentation(const std::string &image_path,
                               const std::string &save_path);
                               */

private:
    void __test_img_read(const std::string &image_path);
    int __generate_random(int start, int end);
    void __get_files(const std::string &path);

private:
    const std::string __engine_file;
    const std::string __config_file;
    const std::string __task_name;
    const std::string __log_file;
    const std::string __log_config_file;
    const std::vector<std::string> __classes;
    yolo::Type __type;
    //log4cplus::Logger __logger;
    std::unique_ptr<yolo::InferApi> __infer;
    std::queue<std::string> __files;
};

#endif
