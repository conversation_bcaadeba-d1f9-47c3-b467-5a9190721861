#ifndef __YOLO_HPP__
#define __YOLO_HPP__

#include "infer.hpp"
#include "preprocess.hpp"
#include "postprocess.hpp"
#include "parse_config.hpp"
#include "spdlog.hpp"

namespace yolo {

using namespace trt;

struct ClassificationMap {
  float logit;
  int label;

  ClassificationMap():logit(0.0), label(-1) {};
  //ClassificationMap(float logit, int label):logit(logit), label(label) {};
  virtual ~ClassificationMap() {};
};

struct Box {
  float left, top, right, bottom, confidence;
  int class_label;

  Box() = default;
  Box(float left, float top, float right, float bottom, float confidence, int class_label): left(left),
            top(top),
            right(right),
            bottom(bottom),
            confidence(confidence),
            class_label(class_label) {}
};

typedef std::vector<Box> BoxArray;

struct OutPut {
    OutPut()=default;

    BoxArray bboxes;
    ClassificationMap cls;  // valid only in classification task
    std::string image_path;
};

typedef std::vector<OutPut> OutArray;

class YoloInfer {
public:
    virtual OutArray forwards(const std::vector<Image> &images,
                              cudaStream_t &stream) = 0;
    bool success=true;
};

class YoloImpl: public YoloInfer {
public:
    YoloImpl(const std::string &engine_file, 
             Type type, ParseConf &conf,
             std::shared_ptr<spdlog::async_logger> logger,
             bool debug_log, bool err_log);

    virtual ~YoloImpl() = default;

    /* override functions */
    virtual OutArray forwards(const std::vector<Image> &images,
                              cudaStream_t &stream) override;
private:
    bool __load(const std::string &engine_file, 
                Type type, 
                float confidence_threshold, 
                float nms_threshold,
                bool show_info);

    void __malloc_memory(int batch_size);
    std::string __enum2string(Type value);

public:
    std::shared_ptr<TrTInfer> trt_;
    Type type_;

    float _confidence_threshold;
    float _nms_threshold;

    std::vector<std::shared_ptr<Memory<uint8_t>>> preprocess_buffers_;
    std::vector<std::shared_ptr<Memory<uint8_t>>> box_segment_cache_;

    Memory<float> input_buffer_;
    Memory<float> bbox_predict_, output_boxarray_;
    Memory<float> segment_predict_;

    size_t network_input_width_, network_input_height_, network_input_channel_;
    size_t _input_size;

    std::vector<int> _bbox_head_dims_;
    std::vector<int> segment_head_dims_;

    int num_classes_ = 0;
    bool has_segment_ = false;
    bool _is_cls = false;
    bool isdynamic_model_ = false;

    /* 
        _num_box_element:
        left, top, right, bottom, confidence, label, 
        keepflag, anchor_idx
    */
    int _num_box_element; 
    int _max_image_boxes; 
    bool _visualize_affine; 

private:
    Preprocess _prep;
    Postprocess _post;

    /* spdlog */
    bool __debug_log, __err_log;
    std::shared_ptr<spdlog::async_logger> __logger;
};


std::shared_ptr<YoloInfer> load(const std::string &engine_file, 
                            Type type, ParseConf &conf,
                            std::shared_ptr<spdlog::async_logger> logger,
                            bool debug_log, bool err_log);


} // namespace yolo

#endif  // __YOLO_HPP__

