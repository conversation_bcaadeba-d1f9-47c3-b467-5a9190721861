#ifndef __POST_HPP
#define __POST_HPP

#include "common.hpp"
#include "util.hpp"
#include "log.hpp"

namespace yolo {

struct InstanceSegmentMap {
  int width = 0, height = 0;      // width % 8 == 0
  unsigned char *data = nullptr;  // is width * height memory

  InstanceSegmentMap(int width, int height);
  virtual ~InstanceSegmentMap();
};

/*
struct Box {
  float left, top, right, bottom, confidence;
  int class_label;

  Box() = default;
  Box(float left, float top, float right, float bottom, float confidence, int class_label)
      : left(left),
        top(top),
        right(right),
        bottom(bottom),
        confidence(confidence),
        class_label(class_label) {}
};

typedef std::vector<Box> BoxArray;
*/

class Postprocess {
public:
    Postprocess() = default;
    Postprocess(float confidence_threshold, float nms_threshold,
                int num_box_element, int gpu_block_threads, 
                int max_image_bboxes):
               _confidence_threshold(confidence_threshold), 
               _nms_threshold(nms_threshold),
               _num_box_element(num_box_element),
               __gpu_block_threads(gpu_block_threads),
               _max_image_bboxes(max_image_bboxes) {}

    inline void set_model_out_params(int num_classes,
                                     int num_anchors,
                                     int result_per_anchor) {
        _num_classes = num_classes;
        _num_anchors = num_anchors;
        _result_per_anchor = result_per_anchor;
    }

    void postprocess(float *predict, 
                     float *affine_matrix_device,
                     float *parray,
                     Type type, 
                     cudaStream_t &stream);

private:
    inline dim3 __grid_dims(int numJobs) {
        int numBlockThreads = numJobs<__gpu_block_threads?numJobs : __gpu_block_threads;
        return dim3(((numJobs + numBlockThreads - 1) / (float)numBlockThreads));
    }

    inline dim3 __block_dims(int numJobs) {
        return numJobs < __gpu_block_threads?numJobs:__gpu_block_threads;
    }

protected:
    float _confidence_threshold;
    float _nms_threshold;

    int _num_classes;
    int _num_anchors; // 2nd dim of network output

    int _result_per_anchor; // 1st dim of network output

    int _num_box_element; 
    int _max_image_bboxes; 

    const int __gpu_block_threads;
};

} // namespace yolo
#endif
