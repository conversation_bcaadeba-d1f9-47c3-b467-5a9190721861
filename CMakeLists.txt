cmake_minimum_required(VERSION 3.10)  # set minimum version of cmake required

project(slice LANGUAGES CXX CUDA)  # set project name
set(CMAKE_BUILD_TYPE Debug)  # enable debug symbols for gdb debugging
#set(CMAKE_BUILD_TYPE Release)  # enable debug symbols for gdb debugging
set(CMAKE_CXX_STANDARD 17)  # set C++ standard to 17
set(CMAKE_CUDA_ARCHITECTURES 75)

set(TRT_PATH "D:\\TensorRT-8.4.3.1")
set(TRT_H ${TRT_PATH}/include)
#set(SYS_TRT "/usr/include/x86_64-linux-gnu/")

# Use find_package to locate the OpenCV library
set(OpenCV_DIR "D:\\opencv3.4.0\\build")
find_package(OpenCV REQUIRED)
find_package(CUDA REQUIRED)

message(STATUS "cuda libs: ${CUDA_LIBRARIES}")

set(THIRD_PARTY "${PROJECT_SOURCE_DIR}\\third_party")
set(PARSER_H "${THIRD_PARTY}\\config_parser\\include")

set(PRO_H "${PROJECT_SOURCE_DIR}\\include")
set(DETECT_H "${PROJECT_SOURCE_DIR}\\test_detection\\include")
set(PARSER_H "${PROJECT_SOURCE_DIR}\\test_detection\\3rd_party_include")

file(GLOB_RECURSE LIB_SRC_FILES
    ${PROJECT_SOURCE_DIR}/lib/slice_preprocess.cu
    ${PROJECT_SOURCE_DIR}/lib/*.cpp)

file(GLOB_RECURSE TEST_SRC_FILES
    ${PROJECT_SOURCE_DIR}/src/*.cpp)

file(GLOB_RECURSE DETECT_SRC_FILES
    ${PROJECT_SOURCE_DIR}/test_detection/*.cpp)

# set include directory
include_directories(PUBLIC ${PRO_H} ${DETECT_H} ${PARSER_H}
    ${TRT_H} ${CUDA_INCLUDE_DIRS} ${OpenCV_INCLUDE_DIRS})

link_directories(${TRT_PATH}/lib ${OpenCV_LIBRARIES})

set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
# create a library target for warpaffine
add_library(slice SHARED ${LIB_SRC_FILES})

add_executable(test ${TEST_SRC_FILES})
target_link_libraries(test PRIVATE slice
    ${OpenCV_LIBRARIES} ${PARSER_LIB} ${CUDA_LIBRARIES}
	nvinfer nvinfer_plugin nvonnxparser)

#add_executable(detect_test ${DETECT_SRC_FILES})
#target_link_libraries(detect_test PRIVATE slice yolo_infer parse_cfg
#    ${OpenCV_LIBRARIES} ${PARSER_LIB} ${CUDA_LIBRARIES} 
#    nvinfer nvinfer_plugin nvonnxparser) 
